
import mongoose, { Schema } from 'mongoose';
import { Document } from 'mongoose';
import { IRevenueCategory, RevenueCategorySchema, RevenueCategory } from './revenue-category.model';

export interface IOrganization extends Document {
    revenueCategory: IRevenueCategory[];
}

const OrganizationSchema = new Schema<IOrganization>({

    revenueCategory: {
        type: [RevenueCategorySchema],
        required: false,
        default: []
    }
}, { timestamps: true });

export const Organization = mongoose.model<IOrganization>('Organization', OrganizationSchema);