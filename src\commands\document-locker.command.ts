import { Command } from 'commander';
import { migrateDocumentLocker } from '../modules/document-locker/document-locker.migration';

export function createDocumentLockerCommand(): Command {
  const documentLockerCommand = new Command('document-locker');
  
  documentLockerCommand
    .description('Migrate document locker data from CSV to MongoDB')
    .option('-d, --database <name>', 'Database name', 'hop-migration')
    .action(async (options) => {
      try {
        await migrateDocumentLocker(options.database);
      } catch (error) {
        console.error('Error executing document locker migration command:', error);
        process.exit(1);
      }
    });
  
  return documentLockerCommand;
}
