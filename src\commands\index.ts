import { Command } from 'commander';
import { createUserCommand } from './user.command';
import { createFacilityCommand } from './facility.command';
import { createServiceCommand } from './service.command';
import { createAppointmentTypeCommand } from './appointment-type.command';
import { createPricingCommand } from './pricing.command';
import { createInvoiceCommand } from './invoice.command';
import { migrateUsers } from '../modules/user/user.migration';
import { migrateFacilities } from '../modules/facility/facility.migration';
import { migrateServices } from '../modules/pricing/service-category.migration';
import { migratePricing } from '../modules/pricing/pricing.migration';
import { migrateInvoices } from '../modules/invoice/invoice.migration';
import logger from '../common/logger/log.module';
import { testCommand } from './test.command';

export function createCommands(): Command {
  try {

    const program = new Command();

    // Add version and description
    program
      .version('1.0.0')
      .description('Migration scripts for HOP application');

    // Test command
    program.addCommand(
      testCommand()
    );

    // Add user command
    program.addCommand(
      createUserCommand()
    );

    // Add facility command
    program.addCommand(
      createFacilityCommand()
    );

    // Add service command
    program.addCommand(
      createServiceCommand()
    );

    // Add appointment type command
    program.addCommand(
      createAppointmentTypeCommand()
    );

    // Add pricing command
    program.addCommand(
      createPricingCommand()
    );

    // Add invoice command
    program.addCommand(
      createInvoiceCommand()
    );

    // Add migrate-all command
    program
      .command('migrate-all')
      .description('Run all migration scripts')
      .option('-d, --database <name>', 'Database name', 'hop-migration')
      .action(async (options) => {
        try {
          console.log('Running all migrations...');

          // Run facility migration
          await migrateFacilities(options.database);

          // Run user migration
          await migrateUsers(options.database);

          // Run services migration (includes appointment types)
          await migrateServices(options.database);

          // Run pricing migration
          await migratePricing(options.database);

          // Run invoice migration
          await migrateInvoices(options.database);

          // Add other migrations here as they are created

          console.log('All migrations completed successfully');
        } catch (error) {
          console.error('Error running all migrations:', error);
          process.exit(1);
        }
      });

    return program;
  } catch (error) {
    logger.error('Error creating commands:', error);
    process.exit(1);
  }
}
