# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@aws-crypto/crc32@5.2.0":
  version "5.2.0"
  resolved "https://registry.yarnpkg.com/@aws-crypto/crc32/-/crc32-5.2.0.tgz#cfcc22570949c98c6689cfcbd2d693d36cdae2e1"
  integrity sha512-nLbCWqQNgUiwwtFsen1AdzAtvuLRsQS8rYgMuxCrdKf9kOssamGLuPwyTY9wyYblNr9+1XM8v6zoDTPPSIeANg==
  dependencies:
    "@aws-crypto/util" "^5.2.0"
    "@aws-sdk/types" "^3.222.0"
    tslib "^2.6.2"

"@aws-crypto/crc32c@5.2.0":
  version "5.2.0"
  resolved "https://registry.yarnpkg.com/@aws-crypto/crc32c/-/crc32c-5.2.0.tgz#4e34aab7f419307821509a98b9b08e84e0c1917e"
  integrity sha512-+iWb8qaHLYKrNvGRbiYRHSdKRWhto5XlZUEBwDjYNf+ly5SVYG6zEoYIdxvf5R3zyeP16w4PLBn3rH1xc74Rag==
  dependencies:
    "@aws-crypto/util" "^5.2.0"
    "@aws-sdk/types" "^3.222.0"
    tslib "^2.6.2"

"@aws-crypto/sha1-browser@5.2.0":
  version "5.2.0"
  resolved "https://registry.yarnpkg.com/@aws-crypto/sha1-browser/-/sha1-browser-5.2.0.tgz#b0ee2d2821d3861f017e965ef3b4cb38e3b6a0f4"
  integrity sha512-OH6lveCFfcDjX4dbAvCFSYUjJZjDr/3XJ3xHtjn3Oj5b9RjojQo8npoLeA/bNwkOkrSQ0wgrHzXk4tDRxGKJeg==
  dependencies:
    "@aws-crypto/supports-web-crypto" "^5.2.0"
    "@aws-crypto/util" "^5.2.0"
    "@aws-sdk/types" "^3.222.0"
    "@aws-sdk/util-locate-window" "^3.0.0"
    "@smithy/util-utf8" "^2.0.0"
    tslib "^2.6.2"

"@aws-crypto/sha256-browser@5.2.0":
  version "5.2.0"
  resolved "https://registry.yarnpkg.com/@aws-crypto/sha256-browser/-/sha256-browser-5.2.0.tgz#153895ef1dba6f9fce38af550e0ef58988eb649e"
  integrity sha512-AXfN/lGotSQwu6HNcEsIASo7kWXZ5HYWvfOmSNKDsEqC4OashTp8alTmaz+F7TC2L083SFv5RdB+qU3Vs1kZqw==
  dependencies:
    "@aws-crypto/sha256-js" "^5.2.0"
    "@aws-crypto/supports-web-crypto" "^5.2.0"
    "@aws-crypto/util" "^5.2.0"
    "@aws-sdk/types" "^3.222.0"
    "@aws-sdk/util-locate-window" "^3.0.0"
    "@smithy/util-utf8" "^2.0.0"
    tslib "^2.6.2"

"@aws-crypto/sha256-js@5.2.0", "@aws-crypto/sha256-js@^5.2.0":
  version "5.2.0"
  resolved "https://registry.yarnpkg.com/@aws-crypto/sha256-js/-/sha256-js-5.2.0.tgz#c4fdb773fdbed9a664fc1a95724e206cf3860042"
  integrity sha512-FFQQyu7edu4ufvIZ+OadFpHHOt+eSTBaYaki44c+akjg7qZg9oOQeLlk77F6tSYqjDAFClrHJk9tMf0HdVyOvA==
  dependencies:
    "@aws-crypto/util" "^5.2.0"
    "@aws-sdk/types" "^3.222.0"
    tslib "^2.6.2"

"@aws-crypto/supports-web-crypto@^5.2.0":
  version "5.2.0"
  resolved "https://registry.yarnpkg.com/@aws-crypto/supports-web-crypto/-/supports-web-crypto-5.2.0.tgz#a1e399af29269be08e695109aa15da0a07b5b5fb"
  integrity sha512-iAvUotm021kM33eCdNfwIN//F77/IADDSs58i+MDaOqFrVjZo9bAal0NK7HurRuWLLpF1iLX7gbWrjHjeo+YFg==
  dependencies:
    tslib "^2.6.2"

"@aws-crypto/util@5.2.0", "@aws-crypto/util@^5.2.0":
  version "5.2.0"
  resolved "https://registry.yarnpkg.com/@aws-crypto/util/-/util-5.2.0.tgz#71284c9cffe7927ddadac793c14f14886d3876da"
  integrity sha512-4RkU9EsI6ZpBve5fseQlGNUWKMa1RLPQ1dnjnQoe07ldfIzcsGb5hC5W0Dm7u423KWzawlrpbjXBrXCEv9zazQ==
  dependencies:
    "@aws-sdk/types" "^3.222.0"
    "@smithy/util-utf8" "^2.0.0"
    tslib "^2.6.2"

"@aws-sdk/client-s3@^3.888.0":
  version "3.888.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/client-s3/-/client-s3-3.888.0.tgz#6a030cc4cc98974062862bd6d490e1b72478057c"
  integrity sha512-MgYyF/qpvCMYVSiOpRJ5C/EtdFxuYAeF5SprtMsbf71xBiiCH5GurB616i+ZxJqHlfhBQTTvR0qugnWvk1Wqvw==
  dependencies:
    "@aws-crypto/sha1-browser" "5.2.0"
    "@aws-crypto/sha256-browser" "5.2.0"
    "@aws-crypto/sha256-js" "5.2.0"
    "@aws-sdk/core" "3.888.0"
    "@aws-sdk/credential-provider-node" "3.888.0"
    "@aws-sdk/middleware-bucket-endpoint" "3.887.0"
    "@aws-sdk/middleware-expect-continue" "3.887.0"
    "@aws-sdk/middleware-flexible-checksums" "3.888.0"
    "@aws-sdk/middleware-host-header" "3.887.0"
    "@aws-sdk/middleware-location-constraint" "3.887.0"
    "@aws-sdk/middleware-logger" "3.887.0"
    "@aws-sdk/middleware-recursion-detection" "3.887.0"
    "@aws-sdk/middleware-sdk-s3" "3.888.0"
    "@aws-sdk/middleware-ssec" "3.887.0"
    "@aws-sdk/middleware-user-agent" "3.888.0"
    "@aws-sdk/region-config-resolver" "3.887.0"
    "@aws-sdk/signature-v4-multi-region" "3.888.0"
    "@aws-sdk/types" "3.887.0"
    "@aws-sdk/util-endpoints" "3.887.0"
    "@aws-sdk/util-user-agent-browser" "3.887.0"
    "@aws-sdk/util-user-agent-node" "3.888.0"
    "@aws-sdk/xml-builder" "3.887.0"
    "@smithy/config-resolver" "^4.2.1"
    "@smithy/core" "^3.11.0"
    "@smithy/eventstream-serde-browser" "^4.1.1"
    "@smithy/eventstream-serde-config-resolver" "^4.2.1"
    "@smithy/eventstream-serde-node" "^4.1.1"
    "@smithy/fetch-http-handler" "^5.2.1"
    "@smithy/hash-blob-browser" "^4.1.1"
    "@smithy/hash-node" "^4.1.1"
    "@smithy/hash-stream-node" "^4.1.1"
    "@smithy/invalid-dependency" "^4.1.1"
    "@smithy/md5-js" "^4.1.1"
    "@smithy/middleware-content-length" "^4.1.1"
    "@smithy/middleware-endpoint" "^4.2.1"
    "@smithy/middleware-retry" "^4.2.1"
    "@smithy/middleware-serde" "^4.1.1"
    "@smithy/middleware-stack" "^4.1.1"
    "@smithy/node-config-provider" "^4.2.1"
    "@smithy/node-http-handler" "^4.2.1"
    "@smithy/protocol-http" "^5.2.1"
    "@smithy/smithy-client" "^4.6.1"
    "@smithy/types" "^4.5.0"
    "@smithy/url-parser" "^4.1.1"
    "@smithy/util-base64" "^4.1.0"
    "@smithy/util-body-length-browser" "^4.1.0"
    "@smithy/util-body-length-node" "^4.1.0"
    "@smithy/util-defaults-mode-browser" "^4.1.1"
    "@smithy/util-defaults-mode-node" "^4.1.1"
    "@smithy/util-endpoints" "^3.1.1"
    "@smithy/util-middleware" "^4.1.1"
    "@smithy/util-retry" "^4.1.1"
    "@smithy/util-stream" "^4.3.1"
    "@smithy/util-utf8" "^4.1.0"
    "@smithy/util-waiter" "^4.1.1"
    "@types/uuid" "^9.0.1"
    tslib "^2.6.2"
    uuid "^9.0.1"

"@aws-sdk/client-sso@3.888.0":
  version "3.888.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/client-sso/-/client-sso-3.888.0.tgz#ab161ff13de9bf43b641df9d04172150761f8418"
  integrity sha512-8CLy/ehGKUmekjH+VtZJ4w40PqDg3u0K7uPziq/4P8Q7LLgsy8YQoHNbuY4am7JU3HWrqLXJI9aaz1+vPGPoWA==
  dependencies:
    "@aws-crypto/sha256-browser" "5.2.0"
    "@aws-crypto/sha256-js" "5.2.0"
    "@aws-sdk/core" "3.888.0"
    "@aws-sdk/middleware-host-header" "3.887.0"
    "@aws-sdk/middleware-logger" "3.887.0"
    "@aws-sdk/middleware-recursion-detection" "3.887.0"
    "@aws-sdk/middleware-user-agent" "3.888.0"
    "@aws-sdk/region-config-resolver" "3.887.0"
    "@aws-sdk/types" "3.887.0"
    "@aws-sdk/util-endpoints" "3.887.0"
    "@aws-sdk/util-user-agent-browser" "3.887.0"
    "@aws-sdk/util-user-agent-node" "3.888.0"
    "@smithy/config-resolver" "^4.2.1"
    "@smithy/core" "^3.11.0"
    "@smithy/fetch-http-handler" "^5.2.1"
    "@smithy/hash-node" "^4.1.1"
    "@smithy/invalid-dependency" "^4.1.1"
    "@smithy/middleware-content-length" "^4.1.1"
    "@smithy/middleware-endpoint" "^4.2.1"
    "@smithy/middleware-retry" "^4.2.1"
    "@smithy/middleware-serde" "^4.1.1"
    "@smithy/middleware-stack" "^4.1.1"
    "@smithy/node-config-provider" "^4.2.1"
    "@smithy/node-http-handler" "^4.2.1"
    "@smithy/protocol-http" "^5.2.1"
    "@smithy/smithy-client" "^4.6.1"
    "@smithy/types" "^4.5.0"
    "@smithy/url-parser" "^4.1.1"
    "@smithy/util-base64" "^4.1.0"
    "@smithy/util-body-length-browser" "^4.1.0"
    "@smithy/util-body-length-node" "^4.1.0"
    "@smithy/util-defaults-mode-browser" "^4.1.1"
    "@smithy/util-defaults-mode-node" "^4.1.1"
    "@smithy/util-endpoints" "^3.1.1"
    "@smithy/util-middleware" "^4.1.1"
    "@smithy/util-retry" "^4.1.1"
    "@smithy/util-utf8" "^4.1.0"
    tslib "^2.6.2"

"@aws-sdk/core@3.888.0":
  version "3.888.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/core/-/core-3.888.0.tgz#d0b9115d5b79b515a0435ff59ad721195bcb55a8"
  integrity sha512-L3S2FZywACo4lmWv37Y4TbefuPJ1fXWyWwIJ3J4wkPYFJ47mmtUPqThlVrSbdTHkEjnZgJe5cRfxk0qCLsFh1w==
  dependencies:
    "@aws-sdk/types" "3.887.0"
    "@aws-sdk/xml-builder" "3.887.0"
    "@smithy/core" "^3.11.0"
    "@smithy/node-config-provider" "^4.2.1"
    "@smithy/property-provider" "^4.0.5"
    "@smithy/protocol-http" "^5.2.1"
    "@smithy/signature-v4" "^5.1.3"
    "@smithy/smithy-client" "^4.6.1"
    "@smithy/types" "^4.5.0"
    "@smithy/util-base64" "^4.1.0"
    "@smithy/util-body-length-browser" "^4.1.0"
    "@smithy/util-middleware" "^4.1.1"
    "@smithy/util-utf8" "^4.1.0"
    fast-xml-parser "5.2.5"
    tslib "^2.6.2"

"@aws-sdk/credential-provider-env@3.888.0":
  version "3.888.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/credential-provider-env/-/credential-provider-env-3.888.0.tgz#20bd28d5ea79d5254829700d9230e0d1a360fdbd"
  integrity sha512-shPi4AhUKbIk7LugJWvNpeZA8va7e5bOHAEKo89S0Ac8WDZt2OaNzbh/b9l0iSL2eEyte8UgIsYGcFxOwIF1VA==
  dependencies:
    "@aws-sdk/core" "3.888.0"
    "@aws-sdk/types" "3.887.0"
    "@smithy/property-provider" "^4.0.5"
    "@smithy/types" "^4.5.0"
    tslib "^2.6.2"

"@aws-sdk/credential-provider-http@3.888.0":
  version "3.888.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/credential-provider-http/-/credential-provider-http-3.888.0.tgz#e32ff8223dbe090bcf004bcc58ec1b676043ccac"
  integrity sha512-Jvuk6nul0lE7o5qlQutcqlySBHLXOyoPtiwE6zyKbGc7RVl0//h39Lab7zMeY2drMn8xAnIopL4606Fd8JI/Hw==
  dependencies:
    "@aws-sdk/core" "3.888.0"
    "@aws-sdk/types" "3.887.0"
    "@smithy/fetch-http-handler" "^5.2.1"
    "@smithy/node-http-handler" "^4.2.1"
    "@smithy/property-provider" "^4.0.5"
    "@smithy/protocol-http" "^5.2.1"
    "@smithy/smithy-client" "^4.6.1"
    "@smithy/types" "^4.5.0"
    "@smithy/util-stream" "^4.3.1"
    tslib "^2.6.2"

"@aws-sdk/credential-provider-ini@3.888.0":
  version "3.888.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/credential-provider-ini/-/credential-provider-ini-3.888.0.tgz#4a97261b8593c3c8c8e5bac974ba0e5e0a40d89f"
  integrity sha512-M82ItvS5yq+tO6ZOV1ruaVs2xOne+v8HW85GFCXnz8pecrzYdgxh6IsVqEbbWruryG/mUGkWMbkBZoEsy4MgyA==
  dependencies:
    "@aws-sdk/core" "3.888.0"
    "@aws-sdk/credential-provider-env" "3.888.0"
    "@aws-sdk/credential-provider-http" "3.888.0"
    "@aws-sdk/credential-provider-process" "3.888.0"
    "@aws-sdk/credential-provider-sso" "3.888.0"
    "@aws-sdk/credential-provider-web-identity" "3.888.0"
    "@aws-sdk/nested-clients" "3.888.0"
    "@aws-sdk/types" "3.887.0"
    "@smithy/credential-provider-imds" "^4.0.7"
    "@smithy/property-provider" "^4.0.5"
    "@smithy/shared-ini-file-loader" "^4.0.5"
    "@smithy/types" "^4.5.0"
    tslib "^2.6.2"

"@aws-sdk/credential-provider-node@3.888.0":
  version "3.888.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/credential-provider-node/-/credential-provider-node-3.888.0.tgz#48f769d52d999088d4437dc1bc76af55afecad9b"
  integrity sha512-KCrQh1dCDC8Y+Ap3SZa6S81kHk+p+yAaOQ5jC3dak4zhHW3RCrsGR/jYdemTOgbEGcA6ye51UbhWfrrlMmeJSA==
  dependencies:
    "@aws-sdk/credential-provider-env" "3.888.0"
    "@aws-sdk/credential-provider-http" "3.888.0"
    "@aws-sdk/credential-provider-ini" "3.888.0"
    "@aws-sdk/credential-provider-process" "3.888.0"
    "@aws-sdk/credential-provider-sso" "3.888.0"
    "@aws-sdk/credential-provider-web-identity" "3.888.0"
    "@aws-sdk/types" "3.887.0"
    "@smithy/credential-provider-imds" "^4.0.7"
    "@smithy/property-provider" "^4.0.5"
    "@smithy/shared-ini-file-loader" "^4.0.5"
    "@smithy/types" "^4.5.0"
    tslib "^2.6.2"

"@aws-sdk/credential-provider-process@3.888.0":
  version "3.888.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/credential-provider-process/-/credential-provider-process-3.888.0.tgz#34842315e44b4882c63eb92fa2151c2efaf5401f"
  integrity sha512-+aX6piSukPQ8DUS4JAH344GePg8/+Q1t0+kvSHAZHhYvtQ/1Zek3ySOJWH2TuzTPCafY4nmWLcQcqvU1w9+4Lw==
  dependencies:
    "@aws-sdk/core" "3.888.0"
    "@aws-sdk/types" "3.887.0"
    "@smithy/property-provider" "^4.0.5"
    "@smithy/shared-ini-file-loader" "^4.0.5"
    "@smithy/types" "^4.5.0"
    tslib "^2.6.2"

"@aws-sdk/credential-provider-sso@3.888.0":
  version "3.888.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/credential-provider-sso/-/credential-provider-sso-3.888.0.tgz#85956b3bcebbcb1aee096d07b4365e524dc1b985"
  integrity sha512-b1ZJji7LJ6E/j1PhFTyvp51in2iCOQ3VP6mj5H6f5OUnqn7efm41iNMoinKr87n0IKZw7qput5ggXVxEdPhouA==
  dependencies:
    "@aws-sdk/client-sso" "3.888.0"
    "@aws-sdk/core" "3.888.0"
    "@aws-sdk/token-providers" "3.888.0"
    "@aws-sdk/types" "3.887.0"
    "@smithy/property-provider" "^4.0.5"
    "@smithy/shared-ini-file-loader" "^4.0.5"
    "@smithy/types" "^4.5.0"
    tslib "^2.6.2"

"@aws-sdk/credential-provider-web-identity@3.888.0":
  version "3.888.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/credential-provider-web-identity/-/credential-provider-web-identity-3.888.0.tgz#03de49dca86649ae2061247ee4d2831642a4767f"
  integrity sha512-7P0QNtsDzMZdmBAaY/vY1BsZHwTGvEz3bsn2bm5VSKFAeMmZqsHK1QeYdNsFjLtegnVh+wodxMq50jqLv3LFlA==
  dependencies:
    "@aws-sdk/core" "3.888.0"
    "@aws-sdk/nested-clients" "3.888.0"
    "@aws-sdk/types" "3.887.0"
    "@smithy/property-provider" "^4.0.5"
    "@smithy/types" "^4.5.0"
    tslib "^2.6.2"

"@aws-sdk/lib-storage@^3.888.0":
  version "3.888.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/lib-storage/-/lib-storage-3.888.0.tgz#d39ee4403282784fc0eea032661f3f657a402326"
  integrity sha512-UCZsVxjnQ6QpVWI1ZiNGmxfd1+8YBNbriQJ5lssR2IudQt5ThGhamGkwoYBmFV4iwhmgIT2fn5+gErugFZuUhw==
  dependencies:
    "@smithy/abort-controller" "^4.0.5"
    "@smithy/middleware-endpoint" "^4.2.1"
    "@smithy/smithy-client" "^4.6.1"
    buffer "5.6.0"
    events "3.3.0"
    stream-browserify "3.0.0"
    tslib "^2.6.2"

"@aws-sdk/middleware-bucket-endpoint@3.887.0":
  version "3.887.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-bucket-endpoint/-/middleware-bucket-endpoint-3.887.0.tgz#a89331e130effd1ee35dec309b8163fb4a9d4920"
  integrity sha512-qRCte/3MtNiMhPh4ZEGk9cHfAXq6IDTflvi2t1tkOIVZFyshkSCvNQNJrrE2D/ljVbOK1f3XbBDaF43EoQzIRQ==
  dependencies:
    "@aws-sdk/types" "3.887.0"
    "@aws-sdk/util-arn-parser" "3.873.0"
    "@smithy/node-config-provider" "^4.2.1"
    "@smithy/protocol-http" "^5.2.1"
    "@smithy/types" "^4.5.0"
    "@smithy/util-config-provider" "^4.0.0"
    tslib "^2.6.2"

"@aws-sdk/middleware-expect-continue@3.887.0":
  version "3.887.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-expect-continue/-/middleware-expect-continue-3.887.0.tgz#cc52bc31752875a8d3dfa84a5705e2b563ffc39f"
  integrity sha512-AlrTZZScDTG9SYeT82BC5cK/6Q4N0miN5xqMW/pbBqP3fNXlsdJOWKB+EKD3V6DV41EV5GVKHKe/1065xKSQ4w==
  dependencies:
    "@aws-sdk/types" "3.887.0"
    "@smithy/protocol-http" "^5.2.1"
    "@smithy/types" "^4.5.0"
    tslib "^2.6.2"

"@aws-sdk/middleware-flexible-checksums@3.888.0":
  version "3.888.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-flexible-checksums/-/middleware-flexible-checksums-3.888.0.tgz#b66da7fe00fe980a9274bc36bd56190a218a4314"
  integrity sha512-vdwd4wMAlXSg1bldhXyTsDSnyPP+bbEVihapejGKNd4gLfyyHwjTfbli+B/hEONGttQs5Dp54UMn8yW/UA189g==
  dependencies:
    "@aws-crypto/crc32" "5.2.0"
    "@aws-crypto/crc32c" "5.2.0"
    "@aws-crypto/util" "5.2.0"
    "@aws-sdk/core" "3.888.0"
    "@aws-sdk/types" "3.887.0"
    "@smithy/is-array-buffer" "^4.0.0"
    "@smithy/node-config-provider" "^4.2.1"
    "@smithy/protocol-http" "^5.2.1"
    "@smithy/types" "^4.5.0"
    "@smithy/util-middleware" "^4.1.1"
    "@smithy/util-stream" "^4.3.1"
    "@smithy/util-utf8" "^4.1.0"
    tslib "^2.6.2"

"@aws-sdk/middleware-host-header@3.887.0":
  version "3.887.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-host-header/-/middleware-host-header-3.887.0.tgz#765305b5a2c412e6bf53eb6d557f2ab831ff50a7"
  integrity sha512-ulzqXv6NNqdu/kr0sgBYupWmahISHY+azpJidtK6ZwQIC+vBUk9NdZeqQpy7KVhIk2xd4+5Oq9rxapPwPI21CA==
  dependencies:
    "@aws-sdk/types" "3.887.0"
    "@smithy/protocol-http" "^5.2.1"
    "@smithy/types" "^4.5.0"
    tslib "^2.6.2"

"@aws-sdk/middleware-location-constraint@3.887.0":
  version "3.887.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-location-constraint/-/middleware-location-constraint-3.887.0.tgz#fdf76f587c04cc8d755f05e41d4df65a78b34127"
  integrity sha512-eU/9Cq4gg2sS32bOomxdx2YF43kb+o70pMhnEBBnVVeqzE8co78SO5FQdWfRTfhNJgTyQ6Vgosx//CNMPIfZPg==
  dependencies:
    "@aws-sdk/types" "3.887.0"
    "@smithy/types" "^4.5.0"
    tslib "^2.6.2"

"@aws-sdk/middleware-logger@3.887.0":
  version "3.887.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-logger/-/middleware-logger-3.887.0.tgz#fec1c731d158306425897b371cfabdf188d07f12"
  integrity sha512-YbbgLI6jKp2qSoAcHnXrQ5jcuc5EYAmGLVFgMVdk8dfCfJLfGGSaOLxF4CXC7QYhO50s+mPPkhBYejCik02Kug==
  dependencies:
    "@aws-sdk/types" "3.887.0"
    "@smithy/types" "^4.5.0"
    tslib "^2.6.2"

"@aws-sdk/middleware-recursion-detection@3.887.0":
  version "3.887.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-recursion-detection/-/middleware-recursion-detection-3.887.0.tgz#4fdb1039042565a4ba0ff506584a99f3c7c3fd23"
  integrity sha512-tjrUXFtQnFLo+qwMveq5faxP5MQakoLArXtqieHphSqZTXm21wDJM73hgT4/PQQGTwgYjDKqnqsE1hvk0hcfDw==
  dependencies:
    "@aws-sdk/types" "3.887.0"
    "@aws/lambda-invoke-store" "^0.0.1"
    "@smithy/protocol-http" "^5.2.1"
    "@smithy/types" "^4.5.0"
    tslib "^2.6.2"

"@aws-sdk/middleware-sdk-s3@3.888.0":
  version "3.888.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-sdk-s3/-/middleware-sdk-s3-3.888.0.tgz#9e561338d9d036a5bf8252b578222b351b635ea1"
  integrity sha512-rKOFNfqgqOfrdcLGF8fcO75azWS2aq2ksRHFoIEFru5FJxzu/yDAhY4C2FKiP/X34xeIUS2SbE/gQgrgWHSN2g==
  dependencies:
    "@aws-sdk/core" "3.888.0"
    "@aws-sdk/types" "3.887.0"
    "@aws-sdk/util-arn-parser" "3.873.0"
    "@smithy/core" "^3.11.0"
    "@smithy/node-config-provider" "^4.2.1"
    "@smithy/protocol-http" "^5.2.1"
    "@smithy/signature-v4" "^5.1.3"
    "@smithy/smithy-client" "^4.6.1"
    "@smithy/types" "^4.5.0"
    "@smithy/util-config-provider" "^4.0.0"
    "@smithy/util-middleware" "^4.1.1"
    "@smithy/util-stream" "^4.3.1"
    "@smithy/util-utf8" "^4.1.0"
    tslib "^2.6.2"

"@aws-sdk/middleware-ssec@3.887.0":
  version "3.887.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-ssec/-/middleware-ssec-3.887.0.tgz#861a3bdb2e0565d492a0869651a348ff36ac5faf"
  integrity sha512-1ixZks0IDkdac1hjPe4vdLSuD9HznkhblCEb4T0wNyw3Ee1fdXg+MlcPWywzG5zkPXLcIrULUzJg/OSYfaDXcQ==
  dependencies:
    "@aws-sdk/types" "3.887.0"
    "@smithy/types" "^4.5.0"
    tslib "^2.6.2"

"@aws-sdk/middleware-user-agent@3.888.0":
  version "3.888.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-user-agent/-/middleware-user-agent-3.888.0.tgz#8b7f8ed11120fd1b931b09de12f7846f72bfe538"
  integrity sha512-ZkcUkoys8AdrNNG7ATjqw2WiXqrhTvT+r4CIK3KhOqIGPHX0p0DQWzqjaIl7ZhSUToKoZ4Ud7MjF795yUr73oA==
  dependencies:
    "@aws-sdk/core" "3.888.0"
    "@aws-sdk/types" "3.887.0"
    "@aws-sdk/util-endpoints" "3.887.0"
    "@smithy/core" "^3.11.0"
    "@smithy/protocol-http" "^5.2.1"
    "@smithy/types" "^4.5.0"
    tslib "^2.6.2"

"@aws-sdk/nested-clients@3.888.0":
  version "3.888.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/nested-clients/-/nested-clients-3.888.0.tgz#5c3ea2517bf05caf4bd699e731f97bf5e565f397"
  integrity sha512-py4o4RPSGt+uwGvSBzR6S6cCBjS4oTX5F8hrHFHfPCdIOMVjyOBejn820jXkCrcdpSj3Qg1yUZXxsByvxc9Lyg==
  dependencies:
    "@aws-crypto/sha256-browser" "5.2.0"
    "@aws-crypto/sha256-js" "5.2.0"
    "@aws-sdk/core" "3.888.0"
    "@aws-sdk/middleware-host-header" "3.887.0"
    "@aws-sdk/middleware-logger" "3.887.0"
    "@aws-sdk/middleware-recursion-detection" "3.887.0"
    "@aws-sdk/middleware-user-agent" "3.888.0"
    "@aws-sdk/region-config-resolver" "3.887.0"
    "@aws-sdk/types" "3.887.0"
    "@aws-sdk/util-endpoints" "3.887.0"
    "@aws-sdk/util-user-agent-browser" "3.887.0"
    "@aws-sdk/util-user-agent-node" "3.888.0"
    "@smithy/config-resolver" "^4.2.1"
    "@smithy/core" "^3.11.0"
    "@smithy/fetch-http-handler" "^5.2.1"
    "@smithy/hash-node" "^4.1.1"
    "@smithy/invalid-dependency" "^4.1.1"
    "@smithy/middleware-content-length" "^4.1.1"
    "@smithy/middleware-endpoint" "^4.2.1"
    "@smithy/middleware-retry" "^4.2.1"
    "@smithy/middleware-serde" "^4.1.1"
    "@smithy/middleware-stack" "^4.1.1"
    "@smithy/node-config-provider" "^4.2.1"
    "@smithy/node-http-handler" "^4.2.1"
    "@smithy/protocol-http" "^5.2.1"
    "@smithy/smithy-client" "^4.6.1"
    "@smithy/types" "^4.5.0"
    "@smithy/url-parser" "^4.1.1"
    "@smithy/util-base64" "^4.1.0"
    "@smithy/util-body-length-browser" "^4.1.0"
    "@smithy/util-body-length-node" "^4.1.0"
    "@smithy/util-defaults-mode-browser" "^4.1.1"
    "@smithy/util-defaults-mode-node" "^4.1.1"
    "@smithy/util-endpoints" "^3.1.1"
    "@smithy/util-middleware" "^4.1.1"
    "@smithy/util-retry" "^4.1.1"
    "@smithy/util-utf8" "^4.1.0"
    tslib "^2.6.2"

"@aws-sdk/region-config-resolver@3.887.0":
  version "3.887.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/region-config-resolver/-/region-config-resolver-3.887.0.tgz#5e89768b44cd5e5c624852ade958579144ac2eb5"
  integrity sha512-VdSMrIqJ3yjJb/fY+YAxrH/lCVv0iL8uA+lbMNfQGtO5tB3Zx6SU9LEpUwBNX8fPK1tUpI65CNE4w42+MY/7Mg==
  dependencies:
    "@aws-sdk/types" "3.887.0"
    "@smithy/node-config-provider" "^4.2.1"
    "@smithy/types" "^4.5.0"
    "@smithy/util-config-provider" "^4.0.0"
    "@smithy/util-middleware" "^4.1.1"
    tslib "^2.6.2"

"@aws-sdk/s3-request-presigner@^3.888.0":
  version "3.888.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/s3-request-presigner/-/s3-request-presigner-3.888.0.tgz#2c629e1bfe1547490873ca71dc38c17fd806a776"
  integrity sha512-3VAF0tJxW0p/ttUzJEgrMe52zZVoEG9dcJGdp4N0RG+LD41lp7QuQEYAZ/LGn7mwJsT0q18+tEJ5XzKmJFrOiA==
  dependencies:
    "@aws-sdk/signature-v4-multi-region" "3.888.0"
    "@aws-sdk/types" "3.887.0"
    "@aws-sdk/util-format-url" "3.887.0"
    "@smithy/middleware-endpoint" "^4.2.1"
    "@smithy/protocol-http" "^5.2.1"
    "@smithy/smithy-client" "^4.6.1"
    "@smithy/types" "^4.5.0"
    tslib "^2.6.2"

"@aws-sdk/signature-v4-multi-region@3.888.0":
  version "3.888.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/signature-v4-multi-region/-/signature-v4-multi-region-3.888.0.tgz#27336bd763746daa3513a8a72436754a370fccce"
  integrity sha512-FmOHUaJzEhqfcpyh0L7HLwYcYopK13Dbmuf+oUyu56/RoeB1nLnltH1VMQVj8v3Am2IwlGR+/JpFyrdkErN+cA==
  dependencies:
    "@aws-sdk/middleware-sdk-s3" "3.888.0"
    "@aws-sdk/types" "3.887.0"
    "@smithy/protocol-http" "^5.2.1"
    "@smithy/signature-v4" "^5.1.3"
    "@smithy/types" "^4.5.0"
    tslib "^2.6.2"

"@aws-sdk/token-providers@3.888.0":
  version "3.888.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/token-providers/-/token-providers-3.888.0.tgz#db79f49c8999c93abab321fbab4e2e6920738b93"
  integrity sha512-WA3NF+3W8GEuCMG1WvkDYbB4z10G3O8xuhT7QSjhvLYWQ9CPt3w4VpVIfdqmUn131TCIbhCzD0KN/1VJTjAjyw==
  dependencies:
    "@aws-sdk/core" "3.888.0"
    "@aws-sdk/nested-clients" "3.888.0"
    "@aws-sdk/types" "3.887.0"
    "@smithy/property-provider" "^4.0.5"
    "@smithy/shared-ini-file-loader" "^4.0.5"
    "@smithy/types" "^4.5.0"
    tslib "^2.6.2"

"@aws-sdk/types@3.887.0", "@aws-sdk/types@^3.222.0":
  version "3.887.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/types/-/types-3.887.0.tgz#989f3b67d7ddb97443e4bdb80ad2313c604b240d"
  integrity sha512-fmTEJpUhsPsovQ12vZSpVTEP/IaRoJAMBGQXlQNjtCpkBp6Iq3KQDa/HDaPINE+3xxo6XvTdtibsNOd5zJLV9A==
  dependencies:
    "@smithy/types" "^4.5.0"
    tslib "^2.6.2"

"@aws-sdk/util-arn-parser@3.873.0":
  version "3.873.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/util-arn-parser/-/util-arn-parser-3.873.0.tgz#12c5ea852574dfb6fe78eaac1666433dff1acffa"
  integrity sha512-qag+VTqnJWDn8zTAXX4wiVioa0hZDQMtbZcGRERVnLar4/3/VIKBhxX2XibNQXFu1ufgcRn4YntT/XEPecFWcg==
  dependencies:
    tslib "^2.6.2"

"@aws-sdk/util-endpoints@3.887.0":
  version "3.887.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/util-endpoints/-/util-endpoints-3.887.0.tgz#e4f2dfb608360b6d8b4e3793492d4625dba00275"
  integrity sha512-kpegvT53KT33BMeIcGLPA65CQVxLUL/C3gTz9AzlU/SDmeusBHX4nRApAicNzI/ltQ5lxZXbQn18UczzBuwF1w==
  dependencies:
    "@aws-sdk/types" "3.887.0"
    "@smithy/types" "^4.5.0"
    "@smithy/url-parser" "^4.1.1"
    "@smithy/util-endpoints" "^3.1.1"
    tslib "^2.6.2"

"@aws-sdk/util-format-url@3.887.0":
  version "3.887.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/util-format-url/-/util-format-url-3.887.0.tgz#53008142379c4a2c813ee1a68ee086a2507874d9"
  integrity sha512-ABDSP6KsrdD+JC7qwMqUpLXqPidvfgT+Q+W8sGGuk/IBy7smgZDOdYSZLE4VBbQpH3N/zSJuslAWhL2x37Qwww==
  dependencies:
    "@aws-sdk/types" "3.887.0"
    "@smithy/querystring-builder" "^4.1.1"
    "@smithy/types" "^4.5.0"
    tslib "^2.6.2"

"@aws-sdk/util-locate-window@^3.0.0":
  version "3.873.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/util-locate-window/-/util-locate-window-3.873.0.tgz#cc10edef3b7aecf365943ec657116d6eb470d9cb"
  integrity sha512-xcVhZF6svjM5Rj89T1WzkjQmrTF6dpR2UvIHPMTnSZoNe6CixejPZ6f0JJ2kAhO8H+dUHwNBlsUgOTIKiK/Syg==
  dependencies:
    tslib "^2.6.2"

"@aws-sdk/util-user-agent-browser@3.887.0":
  version "3.887.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/util-user-agent-browser/-/util-user-agent-browser-3.887.0.tgz#1c5ccc82a0b31a4b159ad98cb12abda1e6c422c8"
  integrity sha512-X71UmVsYc6ZTH4KU6hA5urOzYowSXc3qvroagJNLJYU1ilgZ529lP4J9XOYfEvTXkLR1hPFSRxa43SrwgelMjA==
  dependencies:
    "@aws-sdk/types" "3.887.0"
    "@smithy/types" "^4.5.0"
    bowser "^2.11.0"
    tslib "^2.6.2"

"@aws-sdk/util-user-agent-node@3.888.0":
  version "3.888.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/util-user-agent-node/-/util-user-agent-node-3.888.0.tgz#45c871429c7742cf73f570fbc39ef17f4d12e8de"
  integrity sha512-rSB3OHyuKXotIGfYEo//9sU0lXAUrTY28SUUnxzOGYuQsAt0XR5iYwBAp+RjV6x8f+Hmtbg0PdCsy1iNAXa0UQ==
  dependencies:
    "@aws-sdk/middleware-user-agent" "3.888.0"
    "@aws-sdk/types" "3.887.0"
    "@smithy/node-config-provider" "^4.2.1"
    "@smithy/types" "^4.5.0"
    tslib "^2.6.2"

"@aws-sdk/xml-builder@3.887.0":
  version "3.887.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/xml-builder/-/xml-builder-3.887.0.tgz#376754d19384bbe5b8139c0a0e6521a4a6500c67"
  integrity sha512-lMwgWK1kNgUhHGfBvO/5uLe7TKhycwOn3eRCqsKPT9aPCx/HWuTlpcQp8oW2pCRGLS7qzcxqpQulcD+bbUL7XQ==
  dependencies:
    "@smithy/types" "^4.5.0"
    tslib "^2.6.2"

"@aws/lambda-invoke-store@^0.0.1":
  version "0.0.1"
  resolved "https://registry.yarnpkg.com/@aws/lambda-invoke-store/-/lambda-invoke-store-0.0.1.tgz#92d792a7dda250dfcb902e13228f37a81be57c8f"
  integrity sha512-ORHRQ2tmvnBXc8t/X9Z8IcSbBA4xTLKuN873FopzklHMeqBst7YG0d+AX97inkvDX+NChYtSr+qGfcqGFaI8Zw==

"@cspotcode/source-map-support@^0.8.0":
  version "0.8.1"
  resolved "https://registry.npmjs.org/@cspotcode/source-map-support/-/source-map-support-0.8.1.tgz"
  integrity sha512-IchNf6dN4tHoMFIn/7OE8LWZ19Y6q/67Bmf6vnGREv8RSbBVb9LPJxEcnwrcwX6ixSvaiGoomAUvu4YSxXrVgw==
  dependencies:
    "@jridgewell/trace-mapping" "0.3.9"

"@inquirer/checkbox@^4.1.5":
  version "4.1.5"
  resolved "https://registry.npmjs.org/@inquirer/checkbox/-/checkbox-4.1.5.tgz"
  integrity sha512-swPczVU+at65xa5uPfNP9u3qx/alNwiaykiI/ExpsmMSQW55trmZcwhYWzw/7fj+n6Q8z1eENvR7vFfq9oPSAQ==
  dependencies:
    "@inquirer/core" "^10.1.10"
    "@inquirer/figures" "^1.0.11"
    "@inquirer/type" "^3.0.6"
    ansi-escapes "^4.3.2"
    yoctocolors-cjs "^2.1.2"

"@inquirer/confirm@^5.1.9":
  version "5.1.9"
  resolved "https://registry.npmjs.org/@inquirer/confirm/-/confirm-5.1.9.tgz"
  integrity sha512-NgQCnHqFTjF7Ys2fsqK2WtnA8X1kHyInyG+nMIuHowVTIgIuS10T4AznI/PvbqSpJqjCUqNBlKGh1v3bwLFL4w==
  dependencies:
    "@inquirer/core" "^10.1.10"
    "@inquirer/type" "^3.0.6"

"@inquirer/core@^10.1.10":
  version "10.1.10"
  resolved "https://registry.npmjs.org/@inquirer/core/-/core-10.1.10.tgz"
  integrity sha512-roDaKeY1PYY0aCqhRmXihrHjoSW2A00pV3Ke5fTpMCkzcGF64R8e0lw3dK+eLEHwS4vB5RnW1wuQmvzoRul8Mw==
  dependencies:
    "@inquirer/figures" "^1.0.11"
    "@inquirer/type" "^3.0.6"
    ansi-escapes "^4.3.2"
    cli-width "^4.1.0"
    mute-stream "^2.0.0"
    signal-exit "^4.1.0"
    wrap-ansi "^6.2.0"
    yoctocolors-cjs "^2.1.2"

"@inquirer/editor@^4.2.10":
  version "4.2.10"
  resolved "https://registry.npmjs.org/@inquirer/editor/-/editor-4.2.10.tgz"
  integrity sha512-5GVWJ+qeI6BzR6TIInLP9SXhWCEcvgFQYmcRG6d6RIlhFjM5TyG18paTGBgRYyEouvCmzeco47x9zX9tQEofkw==
  dependencies:
    "@inquirer/core" "^10.1.10"
    "@inquirer/type" "^3.0.6"
    external-editor "^3.1.0"

"@inquirer/expand@^4.0.12":
  version "4.0.12"
  resolved "https://registry.npmjs.org/@inquirer/expand/-/expand-4.0.12.tgz"
  integrity sha512-jV8QoZE1fC0vPe6TnsOfig+qwu7Iza1pkXoUJ3SroRagrt2hxiL+RbM432YAihNR7m7XnU0HWl/WQ35RIGmXHw==
  dependencies:
    "@inquirer/core" "^10.1.10"
    "@inquirer/type" "^3.0.6"
    yoctocolors-cjs "^2.1.2"

"@inquirer/figures@^1.0.11":
  version "1.0.11"
  resolved "https://registry.npmjs.org/@inquirer/figures/-/figures-1.0.11.tgz"
  integrity sha512-eOg92lvrn/aRUqbxRyvpEWnrvRuTYRifixHkYVpJiygTgVSBIHDqLh0SrMQXkafvULg3ck11V7xvR+zcgvpHFw==

"@inquirer/input@^4.1.9":
  version "4.1.9"
  resolved "https://registry.npmjs.org/@inquirer/input/-/input-4.1.9.tgz"
  integrity sha512-mshNG24Ij5KqsQtOZMgj5TwEjIf+F2HOESk6bjMwGWgcH5UBe8UoljwzNFHqdMbGYbgAf6v2wU/X9CAdKJzgOA==
  dependencies:
    "@inquirer/core" "^10.1.10"
    "@inquirer/type" "^3.0.6"

"@inquirer/number@^3.0.12":
  version "3.0.12"
  resolved "https://registry.npmjs.org/@inquirer/number/-/number-3.0.12.tgz"
  integrity sha512-7HRFHxbPCA4e4jMxTQglHJwP+v/kpFsCf2szzfBHy98Wlc3L08HL76UDiA87TOdX5fwj2HMOLWqRWv9Pnn+Z5Q==
  dependencies:
    "@inquirer/core" "^10.1.10"
    "@inquirer/type" "^3.0.6"

"@inquirer/password@^4.0.12":
  version "4.0.12"
  resolved "https://registry.npmjs.org/@inquirer/password/-/password-4.0.12.tgz"
  integrity sha512-FlOB0zvuELPEbnBYiPaOdJIaDzb2PmJ7ghi/SVwIHDDSQ2K4opGBkF+5kXOg6ucrtSUQdLhVVY5tycH0j0l+0g==
  dependencies:
    "@inquirer/core" "^10.1.10"
    "@inquirer/type" "^3.0.6"
    ansi-escapes "^4.3.2"

"@inquirer/prompts@^7.5.0":
  version "7.5.0"
  resolved "https://registry.npmjs.org/@inquirer/prompts/-/prompts-7.5.0.tgz"
  integrity sha512-tk8Bx7l5AX/CR0sVfGj3Xg6v7cYlFBkEahH+EgBB+cZib6Fc83dwerTbzj7f2+qKckjIUGsviWRI1d7lx6nqQA==
  dependencies:
    "@inquirer/checkbox" "^4.1.5"
    "@inquirer/confirm" "^5.1.9"
    "@inquirer/editor" "^4.2.10"
    "@inquirer/expand" "^4.0.12"
    "@inquirer/input" "^4.1.9"
    "@inquirer/number" "^3.0.12"
    "@inquirer/password" "^4.0.12"
    "@inquirer/rawlist" "^4.1.0"
    "@inquirer/search" "^3.0.12"
    "@inquirer/select" "^4.2.0"

"@inquirer/rawlist@^4.1.0":
  version "4.1.0"
  resolved "https://registry.npmjs.org/@inquirer/rawlist/-/rawlist-4.1.0.tgz"
  integrity sha512-6ob45Oh9pXmfprKqUiEeMz/tjtVTFQTgDDz1xAMKMrIvyrYjAmRbQZjMJfsictlL4phgjLhdLu27IkHNnNjB7g==
  dependencies:
    "@inquirer/core" "^10.1.10"
    "@inquirer/type" "^3.0.6"
    yoctocolors-cjs "^2.1.2"

"@inquirer/search@^3.0.12":
  version "3.0.12"
  resolved "https://registry.npmjs.org/@inquirer/search/-/search-3.0.12.tgz"
  integrity sha512-H/kDJA3kNlnNIjB8YsaXoQI0Qccgf0Na14K1h8ExWhNmUg2E941dyFPrZeugihEa9AZNW5NdsD/NcvUME83OPQ==
  dependencies:
    "@inquirer/core" "^10.1.10"
    "@inquirer/figures" "^1.0.11"
    "@inquirer/type" "^3.0.6"
    yoctocolors-cjs "^2.1.2"

"@inquirer/select@^4.2.0":
  version "4.2.0"
  resolved "https://registry.npmjs.org/@inquirer/select/-/select-4.2.0.tgz"
  integrity sha512-KkXQ4aSySWimpV4V/TUJWdB3tdfENZUU765GjOIZ0uPwdbGIG6jrxD4dDf1w68uP+DVtfNhr1A92B+0mbTZ8FA==
  dependencies:
    "@inquirer/core" "^10.1.10"
    "@inquirer/figures" "^1.0.11"
    "@inquirer/type" "^3.0.6"
    ansi-escapes "^4.3.2"
    yoctocolors-cjs "^2.1.2"

"@inquirer/type@^3.0.6":
  version "3.0.6"
  resolved "https://registry.npmjs.org/@inquirer/type/-/type-3.0.6.tgz"
  integrity sha512-/mKVCtVpyBu3IDarv0G+59KC4stsD5mDsGpYh+GKs1NZT88Jh52+cuoA1AtLk2Q0r/quNl+1cSUyLRHBFeD0XA==

"@jridgewell/resolve-uri@^3.0.3":
  version "3.1.2"
  resolved "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz"
  integrity sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==

"@jridgewell/sourcemap-codec@^1.4.10":
  version "1.5.0"
  resolved "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz"
  integrity sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==

"@jridgewell/trace-mapping@0.3.9":
  version "0.3.9"
  resolved "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.9.tgz"
  integrity sha512-3Belt6tdc8bPgAtbcmdtNJlirVoTmEb5e2gC94PnkwEW9jI6CAHUeoG85tjWP5WquqfavoMtMwiG4P926ZKKuQ==
  dependencies:
    "@jridgewell/resolve-uri" "^3.0.3"
    "@jridgewell/sourcemap-codec" "^1.4.10"

"@mapbox/node-pre-gyp@^1.0.11":
  version "1.0.11"
  resolved "https://registry.npmjs.org/@mapbox/node-pre-gyp/-/node-pre-gyp-1.0.11.tgz"
  integrity sha512-Yhlar6v9WQgUp/He7BdgzOz8lqMQ8sU+jkCq7Wx8Myc5YFJLbEe7lgui/V7G1qB1DJykHSGwreceSaD60Y0PUQ==
  dependencies:
    detect-libc "^2.0.0"
    https-proxy-agent "^5.0.0"
    make-dir "^3.1.0"
    node-fetch "^2.6.7"
    nopt "^5.0.0"
    npmlog "^5.0.1"
    rimraf "^3.0.2"
    semver "^7.3.5"
    tar "^6.1.11"

"@mongodb-js/saslprep@^1.1.9":
  version "1.2.0"
  resolved "https://registry.npmjs.org/@mongodb-js/saslprep/-/saslprep-1.2.0.tgz"
  integrity sha512-+ywrb0AqkfaYuhHs6LxKWgqbh3I72EpEgESCw37o+9qPx9WTCkgDm2B+eMrwehGtHBWHFU4GXvnSCNiFhhausg==
  dependencies:
    sparse-bitfield "^3.0.3"

"@smithy/abort-controller@^4.0.5", "@smithy/abort-controller@^4.1.1":
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/@smithy/abort-controller/-/abort-controller-4.1.1.tgz#9b3872ab6b2c061486175c281dadc0a853260533"
  integrity sha512-vkzula+IwRvPR6oKQhMYioM3A/oX/lFCZiwuxkQbRhqJS2S4YRY2k7k/SyR2jMf3607HLtbEwlRxi0ndXHMjRg==
  dependencies:
    "@smithy/types" "^4.5.0"
    tslib "^2.6.2"

"@smithy/chunked-blob-reader-native@^4.1.0":
  version "4.1.0"
  resolved "https://registry.yarnpkg.com/@smithy/chunked-blob-reader-native/-/chunked-blob-reader-native-4.1.0.tgz#4d814dd07ebb1f579daf51671945389f9772400f"
  integrity sha512-Bnv0B3nSlfB2mPO0WgM49I/prl7+kamF042rrf3ezJ3Z4C7csPYvyYgZfXTGXwXfj1mAwDWjE/ybIf49PzFzvA==
  dependencies:
    "@smithy/util-base64" "^4.1.0"
    tslib "^2.6.2"

"@smithy/chunked-blob-reader@^5.1.0":
  version "5.1.0"
  resolved "https://registry.yarnpkg.com/@smithy/chunked-blob-reader/-/chunked-blob-reader-5.1.0.tgz#48fa62c85b146be2a06525f0457ce58a46d69ab0"
  integrity sha512-a36AtR7Q7XOhRPt6F/7HENmTWcB8kN7mDJcOFM/+FuKO6x88w8MQJfYCufMWh4fGyVkPjUh3Rrz/dnqFQdo6OQ==
  dependencies:
    tslib "^2.6.2"

"@smithy/config-resolver@^4.2.1":
  version "4.2.1"
  resolved "https://registry.yarnpkg.com/@smithy/config-resolver/-/config-resolver-4.2.1.tgz#12c24e550e2675e03a78bec64a652ed129bce718"
  integrity sha512-FXil8q4QN7mgKwU2hCLm0ltab8NyY/1RiqEf25Jnf6WLS3wmb11zGAoLETqg1nur2Aoibun4w4MjeN9CMJ4G6A==
  dependencies:
    "@smithy/node-config-provider" "^4.2.1"
    "@smithy/types" "^4.5.0"
    "@smithy/util-config-provider" "^4.1.0"
    "@smithy/util-middleware" "^4.1.1"
    tslib "^2.6.2"

"@smithy/core@^3.11.0":
  version "3.11.0"
  resolved "https://registry.yarnpkg.com/@smithy/core/-/core-3.11.0.tgz#18ee04696ca35889046169e422a894bea1bec59d"
  integrity sha512-Abs5rdP1o8/OINtE49wwNeWuynCu0kme1r4RI3VXVrHr4odVDG7h7mTnw1WXXfN5Il+c25QOnrdL2y56USfxkA==
  dependencies:
    "@smithy/middleware-serde" "^4.1.1"
    "@smithy/protocol-http" "^5.2.1"
    "@smithy/types" "^4.5.0"
    "@smithy/util-base64" "^4.1.0"
    "@smithy/util-body-length-browser" "^4.1.0"
    "@smithy/util-middleware" "^4.1.1"
    "@smithy/util-stream" "^4.3.1"
    "@smithy/util-utf8" "^4.1.0"
    "@types/uuid" "^9.0.1"
    tslib "^2.6.2"
    uuid "^9.0.1"

"@smithy/credential-provider-imds@^4.0.7", "@smithy/credential-provider-imds@^4.1.1":
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/@smithy/credential-provider-imds/-/credential-provider-imds-4.1.1.tgz#e1535a0121a98a2d872eaffc2470eccc057cebd5"
  integrity sha512-1WdBfM9DwA59pnpIizxnUvBf/de18p4GP+6zP2AqrlFzoW3ERpZaT4QueBR0nS9deDMaQRkBlngpVlnkuuTisQ==
  dependencies:
    "@smithy/node-config-provider" "^4.2.1"
    "@smithy/property-provider" "^4.1.1"
    "@smithy/types" "^4.5.0"
    "@smithy/url-parser" "^4.1.1"
    tslib "^2.6.2"

"@smithy/eventstream-codec@^4.1.1":
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/@smithy/eventstream-codec/-/eventstream-codec-4.1.1.tgz#637eb4bceecc3ef588b86c28506439a9cdd7a41f"
  integrity sha512-PwkQw1hZwHTQB6X5hSUWz2OSeuj5Z6enWuAqke7DgWoP3t6vg3ktPpqPz3Erkn6w+tmsl8Oss6nrgyezoea2Iw==
  dependencies:
    "@aws-crypto/crc32" "5.2.0"
    "@smithy/types" "^4.5.0"
    "@smithy/util-hex-encoding" "^4.1.0"
    tslib "^2.6.2"

"@smithy/eventstream-serde-browser@^4.1.1":
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/@smithy/eventstream-serde-browser/-/eventstream-serde-browser-4.1.1.tgz#f7df13ebd5a6028b12b496f12eecdd08c4c9b792"
  integrity sha512-Q9QWdAzRaIuVkefupRPRFAasaG/droBqn1feiMnmLa+LLEUG45pqX1+FurHFmlqiCfobB3nUlgoJfeXZsr7MPA==
  dependencies:
    "@smithy/eventstream-serde-universal" "^4.1.1"
    "@smithy/types" "^4.5.0"
    tslib "^2.6.2"

"@smithy/eventstream-serde-config-resolver@^4.2.1":
  version "4.2.1"
  resolved "https://registry.yarnpkg.com/@smithy/eventstream-serde-config-resolver/-/eventstream-serde-config-resolver-4.2.1.tgz#77333a110361bfe2749b685d31e01299ede87c40"
  integrity sha512-oSUkF9zDN9zcOUBMtxp8RewJlh71E9NoHWU8jE3hU9JMYCsmW4assVTpgic/iS3/dM317j6hO5x18cc3XrfvEw==
  dependencies:
    "@smithy/types" "^4.5.0"
    tslib "^2.6.2"

"@smithy/eventstream-serde-node@^4.1.1":
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/@smithy/eventstream-serde-node/-/eventstream-serde-node-4.1.1.tgz#635819a756cb8a69a7e3eb91ca9076284ea00939"
  integrity sha512-tn6vulwf/ScY0vjhzptSJuDJJqlhNtUjkxJ4wiv9E3SPoEqTEKbaq6bfqRO7nvhTG29ALICRcvfFheOUPl8KNA==
  dependencies:
    "@smithy/eventstream-serde-universal" "^4.1.1"
    "@smithy/types" "^4.5.0"
    tslib "^2.6.2"

"@smithy/eventstream-serde-universal@^4.1.1":
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/@smithy/eventstream-serde-universal/-/eventstream-serde-universal-4.1.1.tgz#803cdde6a17ac501cc700ce38400caf70715ecb1"
  integrity sha512-uLOAiM/Dmgh2CbEXQx+6/ssK7fbzFhd+LjdyFxXid5ZBCbLHTFHLdD/QbXw5aEDsLxQhgzDxLLsZhsftAYwHJA==
  dependencies:
    "@smithy/eventstream-codec" "^4.1.1"
    "@smithy/types" "^4.5.0"
    tslib "^2.6.2"

"@smithy/fetch-http-handler@^5.2.1":
  version "5.2.1"
  resolved "https://registry.yarnpkg.com/@smithy/fetch-http-handler/-/fetch-http-handler-5.2.1.tgz#fe284a00f1b3a35edf9fba454d287b7f74ef20af"
  integrity sha512-5/3wxKNtV3wO/hk1is+CZUhL8a1yy/U+9u9LKQ9kZTkMsHaQjJhc3stFfiujtMnkITjzWfndGA2f7g9Uh9vKng==
  dependencies:
    "@smithy/protocol-http" "^5.2.1"
    "@smithy/querystring-builder" "^4.1.1"
    "@smithy/types" "^4.5.0"
    "@smithy/util-base64" "^4.1.0"
    tslib "^2.6.2"

"@smithy/hash-blob-browser@^4.1.1":
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/@smithy/hash-blob-browser/-/hash-blob-browser-4.1.1.tgz#fbcab0008b973ccf370c698cd11ec8d9584607c8"
  integrity sha512-avAtk++s1e/1VODf+rg7c9R2pB5G9y8yaJaGY4lPZI2+UIqVyuSDMikWjeWfBVmFZ3O7NpDxBbUCyGhThVUKWQ==
  dependencies:
    "@smithy/chunked-blob-reader" "^5.1.0"
    "@smithy/chunked-blob-reader-native" "^4.1.0"
    "@smithy/types" "^4.5.0"
    tslib "^2.6.2"

"@smithy/hash-node@^4.1.1":
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/@smithy/hash-node/-/hash-node-4.1.1.tgz#86ceca92487492267e944e4f4507106b731e7971"
  integrity sha512-H9DIU9WBLhYrvPs9v4sYvnZ1PiAI0oc8CgNQUJ1rpN3pP7QADbTOUjchI2FB764Ub0DstH5xbTqcMJu1pnVqxA==
  dependencies:
    "@smithy/types" "^4.5.0"
    "@smithy/util-buffer-from" "^4.1.0"
    "@smithy/util-utf8" "^4.1.0"
    tslib "^2.6.2"

"@smithy/hash-stream-node@^4.1.1":
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/@smithy/hash-stream-node/-/hash-stream-node-4.1.1.tgz#1d8e4485fa15c458d7a8248a50d0f5f91705cced"
  integrity sha512-3ztT4pV0Moazs3JAYFdfKk11kYFDo4b/3R3+xVjIm6wY9YpJf+xfz+ocEnNKcWAdcmSMqi168i2EMaKmJHbJMA==
  dependencies:
    "@smithy/types" "^4.5.0"
    "@smithy/util-utf8" "^4.1.0"
    tslib "^2.6.2"

"@smithy/invalid-dependency@^4.1.1":
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/@smithy/invalid-dependency/-/invalid-dependency-4.1.1.tgz#2511335ff889944701c7d2a3b1e4a4d6fe9ddfab"
  integrity sha512-1AqLyFlfrrDkyES8uhINRlJXmHA2FkG+3DY8X+rmLSqmFwk3DJnvhyGzyByPyewh2jbmV+TYQBEfngQax8IFGg==
  dependencies:
    "@smithy/types" "^4.5.0"
    tslib "^2.6.2"

"@smithy/is-array-buffer@^2.2.0":
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/@smithy/is-array-buffer/-/is-array-buffer-2.2.0.tgz#f84f0d9f9a36601a9ca9381688bd1b726fd39111"
  integrity sha512-GGP3O9QFD24uGeAXYUjwSTXARoqpZykHadOmA8G5vfJPK0/DC67qa//0qvqrJzL1xc8WQWX7/yc7fwudjPHPhA==
  dependencies:
    tslib "^2.6.2"

"@smithy/is-array-buffer@^4.0.0", "@smithy/is-array-buffer@^4.1.0":
  version "4.1.0"
  resolved "https://registry.yarnpkg.com/@smithy/is-array-buffer/-/is-array-buffer-4.1.0.tgz#d18a2f22280e7173633cb91a9bdb6f3d8a6560b8"
  integrity sha512-ePTYUOV54wMogio+he4pBybe8fwg4sDvEVDBU8ZlHOZXbXK3/C0XfJgUCu6qAZcawv05ZhZzODGUerFBPsPUDQ==
  dependencies:
    tslib "^2.6.2"

"@smithy/md5-js@^4.1.1":
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/@smithy/md5-js/-/md5-js-4.1.1.tgz#df81396bef83eb17bce531c871af935df986bdfc"
  integrity sha512-MvWXKK743BuHjr/hnWuT6uStdKEaoqxHAQUvbKJPPZM5ZojTNFI5D+47BoQfBE5RgGlRRty05EbWA+NXDv+hIA==
  dependencies:
    "@smithy/types" "^4.5.0"
    "@smithy/util-utf8" "^4.1.0"
    tslib "^2.6.2"

"@smithy/middleware-content-length@^4.1.1":
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/@smithy/middleware-content-length/-/middleware-content-length-4.1.1.tgz#eaea7bd14c7a0b64aef87b8c372c2a04d7b9cb72"
  integrity sha512-9wlfBBgTsRvC2JxLJxv4xDGNBrZuio3AgSl0lSFX7fneW2cGskXTYpFxCdRYD2+5yzmsiTuaAJD1Wp7gWt9y9w==
  dependencies:
    "@smithy/protocol-http" "^5.2.1"
    "@smithy/types" "^4.5.0"
    tslib "^2.6.2"

"@smithy/middleware-endpoint@^4.2.1":
  version "4.2.1"
  resolved "https://registry.yarnpkg.com/@smithy/middleware-endpoint/-/middleware-endpoint-4.2.1.tgz#54c61a113e6da7b615724d03517879d377d3888d"
  integrity sha512-fUTMmQvQQZakXOuKizfu7fBLDpwvWZjfH6zUK2OLsoNZRZGbNUdNSdLJHpwk1vS208jtDjpUIskh+JoA8zMzZg==
  dependencies:
    "@smithy/core" "^3.11.0"
    "@smithy/middleware-serde" "^4.1.1"
    "@smithy/node-config-provider" "^4.2.1"
    "@smithy/shared-ini-file-loader" "^4.1.1"
    "@smithy/types" "^4.5.0"
    "@smithy/url-parser" "^4.1.1"
    "@smithy/util-middleware" "^4.1.1"
    tslib "^2.6.2"

"@smithy/middleware-retry@^4.2.1":
  version "4.2.1"
  resolved "https://registry.yarnpkg.com/@smithy/middleware-retry/-/middleware-retry-4.2.1.tgz#61be10c06e183c392a3769cb8b03c7846b37bee7"
  integrity sha512-JzfvjwSJXWRl7LkLgIRTUTd2Wj639yr3sQGpViGNEOjtb0AkAuYqRAHs+jSOI/LPC0ZTjmFVVtfrCICMuebexw==
  dependencies:
    "@smithy/node-config-provider" "^4.2.1"
    "@smithy/protocol-http" "^5.2.1"
    "@smithy/service-error-classification" "^4.1.1"
    "@smithy/smithy-client" "^4.6.1"
    "@smithy/types" "^4.5.0"
    "@smithy/util-middleware" "^4.1.1"
    "@smithy/util-retry" "^4.1.1"
    "@types/uuid" "^9.0.1"
    tslib "^2.6.2"
    uuid "^9.0.1"

"@smithy/middleware-serde@^4.1.1":
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/@smithy/middleware-serde/-/middleware-serde-4.1.1.tgz#cfb99f53c744d7730928235cbe66cc7ff8a8a9b2"
  integrity sha512-lh48uQdbCoj619kRouev5XbWhCwRKLmphAif16c4J6JgJ4uXjub1PI6RL38d3BLliUvSso6klyB/LTNpWSNIyg==
  dependencies:
    "@smithy/protocol-http" "^5.2.1"
    "@smithy/types" "^4.5.0"
    tslib "^2.6.2"

"@smithy/middleware-stack@^4.1.1":
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/@smithy/middleware-stack/-/middleware-stack-4.1.1.tgz#1d533fde4ccbb62d7fc0f0b8ac518b7e4791e311"
  integrity sha512-ygRnniqNcDhHzs6QAPIdia26M7e7z9gpkIMUe/pK0RsrQ7i5MblwxY8078/QCnGq6AmlUUWgljK2HlelsKIb/A==
  dependencies:
    "@smithy/types" "^4.5.0"
    tslib "^2.6.2"

"@smithy/node-config-provider@^4.2.1":
  version "4.2.1"
  resolved "https://registry.yarnpkg.com/@smithy/node-config-provider/-/node-config-provider-4.2.1.tgz#31be8865dbea9a9f23aee278a6728317d0ed0250"
  integrity sha512-AIA0BJZq2h295J5NeCTKhg1WwtdTA/GqBCaVjk30bDgMHwniUETyh5cP9IiE9VrId7Kt8hS7zvREVMTv1VfA6g==
  dependencies:
    "@smithy/property-provider" "^4.1.1"
    "@smithy/shared-ini-file-loader" "^4.1.1"
    "@smithy/types" "^4.5.0"
    tslib "^2.6.2"

"@smithy/node-http-handler@^4.2.1":
  version "4.2.1"
  resolved "https://registry.yarnpkg.com/@smithy/node-http-handler/-/node-http-handler-4.2.1.tgz#d7ab8e31659030d3d5a68f0982f15c00b1e67a0c"
  integrity sha512-REyybygHlxo3TJICPF89N2pMQSf+p+tBJqpVe1+77Cfi9HBPReNjTgtZ1Vg73exq24vkqJskKDpfF74reXjxfw==
  dependencies:
    "@smithy/abort-controller" "^4.1.1"
    "@smithy/protocol-http" "^5.2.1"
    "@smithy/querystring-builder" "^4.1.1"
    "@smithy/types" "^4.5.0"
    tslib "^2.6.2"

"@smithy/property-provider@^4.0.5", "@smithy/property-provider@^4.1.1":
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/@smithy/property-provider/-/property-provider-4.1.1.tgz#6e11ae6729840314afed05fd6ab48f62c654116b"
  integrity sha512-gm3ZS7DHxUbzC2wr8MUCsAabyiXY0gaj3ROWnhSx/9sPMc6eYLMM4rX81w1zsMaObj2Lq3PZtNCC1J6lpEY7zg==
  dependencies:
    "@smithy/types" "^4.5.0"
    tslib "^2.6.2"

"@smithy/protocol-http@^5.2.1":
  version "5.2.1"
  resolved "https://registry.yarnpkg.com/@smithy/protocol-http/-/protocol-http-5.2.1.tgz#33f2b8e4e1082c3ae0372d1322577e6fa71d7824"
  integrity sha512-T8SlkLYCwfT/6m33SIU/JOVGNwoelkrvGjFKDSDtVvAXj/9gOT78JVJEas5a+ETjOu4SVvpCstKgd0PxSu/aHw==
  dependencies:
    "@smithy/types" "^4.5.0"
    tslib "^2.6.2"

"@smithy/querystring-builder@^4.1.1":
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/@smithy/querystring-builder/-/querystring-builder-4.1.1.tgz#4d35c1735de8214055424045a117fa5d1d5cdec1"
  integrity sha512-J9b55bfimP4z/Jg1gNo+AT84hr90p716/nvxDkPGCD4W70MPms0h8KF50RDRgBGZeL83/u59DWNqJv6tEP/DHA==
  dependencies:
    "@smithy/types" "^4.5.0"
    "@smithy/util-uri-escape" "^4.1.0"
    tslib "^2.6.2"

"@smithy/querystring-parser@^4.1.1":
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/@smithy/querystring-parser/-/querystring-parser-4.1.1.tgz#21b861439b2db16abeb0a6789b126705fa25eea1"
  integrity sha512-63TEp92YFz0oQ7Pj9IuI3IgnprP92LrZtRAkE3c6wLWJxfy/yOPRt39IOKerVr0JS770olzl0kGafXlAXZ1vng==
  dependencies:
    "@smithy/types" "^4.5.0"
    tslib "^2.6.2"

"@smithy/service-error-classification@^4.1.1":
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/@smithy/service-error-classification/-/service-error-classification-4.1.1.tgz#86a615298ae406c3b6c7dc63c1c1738c54cfdfc6"
  integrity sha512-Iam75b/JNXyDE41UvrlM6n8DNOa/r1ylFyvgruTUx7h2Uk7vDNV9AAwP1vfL1fOL8ls0xArwEGVcGZVd7IO/Cw==
  dependencies:
    "@smithy/types" "^4.5.0"

"@smithy/shared-ini-file-loader@^4.0.5", "@smithy/shared-ini-file-loader@^4.1.1":
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/@smithy/shared-ini-file-loader/-/shared-ini-file-loader-4.1.1.tgz#d4a748bb8027e1111635464c9b1e546d608fc089"
  integrity sha512-YkpikhIqGc4sfXeIbzSj10t2bJI/sSoP5qxLue6zG+tEE3ngOBSm8sO3+djacYvS/R5DfpxN/L9CyZsvwjWOAQ==
  dependencies:
    "@smithy/types" "^4.5.0"
    tslib "^2.6.2"

"@smithy/signature-v4@^5.1.3":
  version "5.2.1"
  resolved "https://registry.yarnpkg.com/@smithy/signature-v4/-/signature-v4-5.2.1.tgz#0048489d2f1b3c888382595a085edd31967498f8"
  integrity sha512-M9rZhWQLjlQVCCR37cSjHfhriGRN+FQ8UfgrYNufv66TJgk+acaggShl3KS5U/ssxivvZLlnj7QH2CUOKlxPyA==
  dependencies:
    "@smithy/is-array-buffer" "^4.1.0"
    "@smithy/protocol-http" "^5.2.1"
    "@smithy/types" "^4.5.0"
    "@smithy/util-hex-encoding" "^4.1.0"
    "@smithy/util-middleware" "^4.1.1"
    "@smithy/util-uri-escape" "^4.1.0"
    "@smithy/util-utf8" "^4.1.0"
    tslib "^2.6.2"

"@smithy/smithy-client@^4.6.1":
  version "4.6.1"
  resolved "https://registry.yarnpkg.com/@smithy/smithy-client/-/smithy-client-4.6.1.tgz#4bebcf313431bd274da0b28c7ddc4ba335f9994b"
  integrity sha512-WolVLDb9UTPMEPPOncrCt6JmAMCSC/V2y5gst2STWJ5r7+8iNac+EFYQnmvDCYMfOLcilOSEpm5yXZXwbLak1Q==
  dependencies:
    "@smithy/core" "^3.11.0"
    "@smithy/middleware-endpoint" "^4.2.1"
    "@smithy/middleware-stack" "^4.1.1"
    "@smithy/protocol-http" "^5.2.1"
    "@smithy/types" "^4.5.0"
    "@smithy/util-stream" "^4.3.1"
    tslib "^2.6.2"

"@smithy/types@^4.5.0":
  version "4.5.0"
  resolved "https://registry.yarnpkg.com/@smithy/types/-/types-4.5.0.tgz#850e334662a1ef1286c35814940c80880400a370"
  integrity sha512-RkUpIOsVlAwUIZXO1dsz8Zm+N72LClFfsNqf173catVlvRZiwPy0x2u0JLEA4byreOPKDZPGjmPDylMoP8ZJRg==
  dependencies:
    tslib "^2.6.2"

"@smithy/url-parser@^4.1.1":
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/@smithy/url-parser/-/url-parser-4.1.1.tgz#0e9a5e72b3cf9d7ab7305f9093af5528d9debaf6"
  integrity sha512-bx32FUpkhcaKlEoOMbScvc93isaSiRM75pQ5IgIBaMkT7qMlIibpPRONyx/0CvrXHzJLpOn/u6YiDX2hcvs7Dg==
  dependencies:
    "@smithy/querystring-parser" "^4.1.1"
    "@smithy/types" "^4.5.0"
    tslib "^2.6.2"

"@smithy/util-base64@^4.1.0":
  version "4.1.0"
  resolved "https://registry.yarnpkg.com/@smithy/util-base64/-/util-base64-4.1.0.tgz#5965026081d9aef4a8246f5702807570abe538b2"
  integrity sha512-RUGd4wNb8GeW7xk+AY5ghGnIwM96V0l2uzvs/uVHf+tIuVX2WSvynk5CxNoBCsM2rQRSZElAo9rt3G5mJ/gktQ==
  dependencies:
    "@smithy/util-buffer-from" "^4.1.0"
    "@smithy/util-utf8" "^4.1.0"
    tslib "^2.6.2"

"@smithy/util-body-length-browser@^4.1.0":
  version "4.1.0"
  resolved "https://registry.yarnpkg.com/@smithy/util-body-length-browser/-/util-body-length-browser-4.1.0.tgz#636bdf4bc878c546627dab4b9b0e4db31b475be7"
  integrity sha512-V2E2Iez+bo6bUMOTENPr6eEmepdY8Hbs+Uc1vkDKgKNA/brTJqOW/ai3JO1BGj9GbCeLqw90pbbH7HFQyFotGQ==
  dependencies:
    tslib "^2.6.2"

"@smithy/util-body-length-node@^4.1.0":
  version "4.1.0"
  resolved "https://registry.yarnpkg.com/@smithy/util-body-length-node/-/util-body-length-node-4.1.0.tgz#646750e4af58f97254a5d5cfeaba7d992f0152ec"
  integrity sha512-BOI5dYjheZdgR9XiEM3HJcEMCXSoqbzu7CzIgYrx0UtmvtC3tC2iDGpJLsSRFffUpy8ymsg2ARMP5fR8mtuUQQ==
  dependencies:
    tslib "^2.6.2"

"@smithy/util-buffer-from@^2.2.0":
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/@smithy/util-buffer-from/-/util-buffer-from-2.2.0.tgz#6fc88585165ec73f8681d426d96de5d402021e4b"
  integrity sha512-IJdWBbTcMQ6DA0gdNhh/BwrLkDR+ADW5Kr1aZmd4k3DIF6ezMV4R2NIAmT08wQJ3yUK82thHWmC/TnK/wpMMIA==
  dependencies:
    "@smithy/is-array-buffer" "^2.2.0"
    tslib "^2.6.2"

"@smithy/util-buffer-from@^4.1.0":
  version "4.1.0"
  resolved "https://registry.yarnpkg.com/@smithy/util-buffer-from/-/util-buffer-from-4.1.0.tgz#21f9e644a0eb41226d92e4eff763f76a7db7e9cc"
  integrity sha512-N6yXcjfe/E+xKEccWEKzK6M+crMrlwaCepKja0pNnlSkm6SjAeLKKA++er5Ba0I17gvKfN/ThV+ZOx/CntKTVw==
  dependencies:
    "@smithy/is-array-buffer" "^4.1.0"
    tslib "^2.6.2"

"@smithy/util-config-provider@^4.0.0", "@smithy/util-config-provider@^4.1.0":
  version "4.1.0"
  resolved "https://registry.yarnpkg.com/@smithy/util-config-provider/-/util-config-provider-4.1.0.tgz#6a07d73446c1e9a46d7a3c125f2a9301060bc957"
  integrity sha512-swXz2vMjrP1ZusZWVTB/ai5gK+J8U0BWvP10v9fpcFvg+Xi/87LHvHfst2IgCs1i0v4qFZfGwCmeD/KNCdJZbQ==
  dependencies:
    tslib "^2.6.2"

"@smithy/util-defaults-mode-browser@^4.1.1":
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/@smithy/util-defaults-mode-browser/-/util-defaults-mode-browser-4.1.1.tgz#40b9659d6fc15aa1101e440d1a92579cb66ebfa3"
  integrity sha512-hA1AKIHFUMa9Tl6q6y8p0pJ9aWHCCG8s57flmIyLE0W7HcJeYrYtnqXDcGnftvXEhdQnSexyegXnzzTGk8bKLA==
  dependencies:
    "@smithy/property-provider" "^4.1.1"
    "@smithy/smithy-client" "^4.6.1"
    "@smithy/types" "^4.5.0"
    bowser "^2.11.0"
    tslib "^2.6.2"

"@smithy/util-defaults-mode-node@^4.1.1":
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/@smithy/util-defaults-mode-node/-/util-defaults-mode-node-4.1.1.tgz#bca834c5ee16949bf8d0db9ac7bf988ad0d3ce10"
  integrity sha512-RGSpmoBrA+5D2WjwtK7tto6Pc2wO9KSXKLpLONhFZ8VyuCbqlLdiDAfuDTNY9AJe4JoE+Cx806cpTQQoQ71zPQ==
  dependencies:
    "@smithy/config-resolver" "^4.2.1"
    "@smithy/credential-provider-imds" "^4.1.1"
    "@smithy/node-config-provider" "^4.2.1"
    "@smithy/property-provider" "^4.1.1"
    "@smithy/smithy-client" "^4.6.1"
    "@smithy/types" "^4.5.0"
    tslib "^2.6.2"

"@smithy/util-endpoints@^3.1.1":
  version "3.1.1"
  resolved "https://registry.yarnpkg.com/@smithy/util-endpoints/-/util-endpoints-3.1.1.tgz#62c7e10e3a685c9cbb4080220d9e819ee79be8ff"
  integrity sha512-qB4R9kO0SetA11Rzu6MVGFIaGYX3p6SGGGfWwsKnC6nXIf0n/0AKVwRTsYsz9ToN8CeNNtNgQRwKFBndGJZdyw==
  dependencies:
    "@smithy/node-config-provider" "^4.2.1"
    "@smithy/types" "^4.5.0"
    tslib "^2.6.2"

"@smithy/util-hex-encoding@^4.1.0":
  version "4.1.0"
  resolved "https://registry.yarnpkg.com/@smithy/util-hex-encoding/-/util-hex-encoding-4.1.0.tgz#9b27cf0c25d0de2c8ebfe75cc20df84e5014ccc9"
  integrity sha512-1LcueNN5GYC4tr8mo14yVYbh/Ur8jHhWOxniZXii+1+ePiIbsLZ5fEI0QQGtbRRP5mOhmooos+rLmVASGGoq5w==
  dependencies:
    tslib "^2.6.2"

"@smithy/util-middleware@^4.1.1":
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/@smithy/util-middleware/-/util-middleware-4.1.1.tgz#e19749a127499c9bdada713a8afd807d92d846e2"
  integrity sha512-CGmZ72mL29VMfESz7S6dekqzCh8ZISj3B+w0g1hZFXaOjGTVaSqfAEFAq8EGp8fUL+Q2l8aqNmt8U1tglTikeg==
  dependencies:
    "@smithy/types" "^4.5.0"
    tslib "^2.6.2"

"@smithy/util-retry@^4.1.1":
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/@smithy/util-retry/-/util-retry-4.1.1.tgz#f4a99d9b0ffb9e4bb119ac5a24e54e54d891e22c"
  integrity sha512-jGeybqEZ/LIordPLMh5bnmnoIgsqnp4IEimmUp5c5voZ8yx+5kAlN5+juyr7p+f7AtZTgvhmInQk4Q0UVbrZ0Q==
  dependencies:
    "@smithy/service-error-classification" "^4.1.1"
    "@smithy/types" "^4.5.0"
    tslib "^2.6.2"

"@smithy/util-stream@^4.3.1":
  version "4.3.1"
  resolved "https://registry.yarnpkg.com/@smithy/util-stream/-/util-stream-4.3.1.tgz#63cce0f09d99d75142c6dc8fe03e55ac0171de47"
  integrity sha512-khKkW/Jqkgh6caxMWbMuox9+YfGlsk9OnHOYCGVEdYQb/XVzcORXHLYUubHmmda0pubEDncofUrPNniS9d+uAA==
  dependencies:
    "@smithy/fetch-http-handler" "^5.2.1"
    "@smithy/node-http-handler" "^4.2.1"
    "@smithy/types" "^4.5.0"
    "@smithy/util-base64" "^4.1.0"
    "@smithy/util-buffer-from" "^4.1.0"
    "@smithy/util-hex-encoding" "^4.1.0"
    "@smithy/util-utf8" "^4.1.0"
    tslib "^2.6.2"

"@smithy/util-uri-escape@^4.1.0":
  version "4.1.0"
  resolved "https://registry.yarnpkg.com/@smithy/util-uri-escape/-/util-uri-escape-4.1.0.tgz#ed4a5c498f1da07122ca1e3df4ca3e2c67c6c18a"
  integrity sha512-b0EFQkq35K5NHUYxU72JuoheM6+pytEVUGlTwiFxWFpmddA+Bpz3LgsPRIpBk8lnPE47yT7AF2Egc3jVnKLuPg==
  dependencies:
    tslib "^2.6.2"

"@smithy/util-utf8@^2.0.0":
  version "2.3.0"
  resolved "https://registry.yarnpkg.com/@smithy/util-utf8/-/util-utf8-2.3.0.tgz#dd96d7640363259924a214313c3cf16e7dd329c5"
  integrity sha512-R8Rdn8Hy72KKcebgLiv8jQcQkXoLMOGGv5uI1/k0l+snqkOzQ1R0ChUBCxWMlBsFMekWjq0wRudIweFs7sKT5A==
  dependencies:
    "@smithy/util-buffer-from" "^2.2.0"
    tslib "^2.6.2"

"@smithy/util-utf8@^4.1.0":
  version "4.1.0"
  resolved "https://registry.yarnpkg.com/@smithy/util-utf8/-/util-utf8-4.1.0.tgz#912c33c1a06913f39daa53da79cb8f7ab740d97b"
  integrity sha512-mEu1/UIXAdNYuBcyEPbjScKi/+MQVXNIuY/7Cm5XLIWe319kDrT5SizBE95jqtmEXoDbGoZxKLCMttdZdqTZKQ==
  dependencies:
    "@smithy/util-buffer-from" "^4.1.0"
    tslib "^2.6.2"

"@smithy/util-waiter@^4.1.1":
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/@smithy/util-waiter/-/util-waiter-4.1.1.tgz#5b74429ca9e37f61838800b919d0063b1a865bef"
  integrity sha512-PJBmyayrlfxM7nbqjomF4YcT1sApQwZio0NHSsT0EzhJqljRmvhzqZua43TyEs80nJk2Cn2FGPg/N8phH6KeCQ==
  dependencies:
    "@smithy/abort-controller" "^4.1.1"
    "@smithy/types" "^4.5.0"
    tslib "^2.6.2"

"@tsconfig/node10@^1.0.7":
  version "1.0.11"
  resolved "https://registry.npmjs.org/@tsconfig/node10/-/node10-1.0.11.tgz"
  integrity sha512-DcRjDCujK/kCk/cUe8Xz8ZSpm8mS3mNNpta+jGCA6USEDfktlNvm1+IuZ9eTcDbNk41BHwpHHeW+N1lKCz4zOw==

"@tsconfig/node12@^1.0.7":
  version "1.0.11"
  resolved "https://registry.npmjs.org/@tsconfig/node12/-/node12-1.0.11.tgz"
  integrity sha512-cqefuRsh12pWyGsIoBKJA9luFu3mRxCA+ORZvA4ktLSzIuCUtWVxGIuXigEwO5/ywWFMZ2QEGKWvkZG1zDMTag==

"@tsconfig/node14@^1.0.0":
  version "1.0.3"
  resolved "https://registry.npmjs.org/@tsconfig/node14/-/node14-1.0.3.tgz"
  integrity sha512-ysT8mhdixWK6Hw3i1V2AeRqZ5WfXg1G43mqoYlM2nc6388Fq5jcXyr5mRsqViLx/GJYdoL0bfXD8nmF+Zn/Iow==

"@tsconfig/node16@^1.0.2":
  version "1.0.4"
  resolved "https://registry.npmjs.org/@tsconfig/node16/-/node16-1.0.4.tgz"
  integrity sha512-vxhUy4J8lyeyinH7Azl1pdd43GJhZH/tP2weN8TntQblOY+A0XbT8DJk1/oCPuOOyg/Ja757rG0CgHcWC8OfMA==

"@types/bcrypt@^5.0.2":
  version "5.0.2"
  resolved "https://registry.npmjs.org/@types/bcrypt/-/bcrypt-5.0.2.tgz"
  integrity sha512-6atioO8Y75fNcbmj0G7UjI9lXN2pQ/IGJ2FWT4a/btd0Lk9lQalHLKhkgKVZ3r+spnmWUKfbMi1GEe9wyHQfNQ==
  dependencies:
    "@types/node" "*"

"@types/node@*", "@types/node@^22.13.10":
  version "22.13.10"
  resolved "https://registry.npmjs.org/@types/node/-/node-22.13.10.tgz"
  integrity sha512-I6LPUvlRH+O6VRUqYOcMudhaIdUVWfsjnZavnsraHvpBwaEyMN29ry+0UVJhImYL16xsscu0aske3yA+uPOWfw==
  dependencies:
    undici-types "~6.20.0"

"@types/uuid4@^2.0.3":
  version "2.0.3"
  resolved "https://registry.yarnpkg.com/@types/uuid4/-/uuid4-2.0.3.tgz#0dd3e35f4ee1d379d3f29c09f5d94fa405ad36b4"
  integrity sha512-/fyn8jzKzeL/wci7GOaz8TPjKapD+WJUBUCr/ED2xcUcx5fA9rZH2fDZiV2Z/a+040mp5Zi3dgIi/Vey/uQBxw==

"@types/uuid@^9.0.1":
  version "9.0.8"
  resolved "https://registry.yarnpkg.com/@types/uuid/-/uuid-9.0.8.tgz#7545ba4fc3c003d6c756f651f3bf163d8f0f29ba"
  integrity sha512-jg+97EGIcY9AGHJJRaaPVgetKDsrTgbRjQ5Msgjh/DQKEFl0DtyRr/VCOyD1T2R1MNeWPK/u7JoGhlDZnKBAfA==

"@types/validator@^13.15.3":
  version "13.15.3"
  resolved "https://registry.yarnpkg.com/@types/validator/-/validator-13.15.3.tgz#67e8aeacbace03517f9bd3f99e750bb666207ff4"
  integrity sha512-7bcUmDyS6PN3EuD9SlGGOxM77F8WLVsrwkxyWxKnxzmXoequ6c7741QBrANq6htVRGOITJ7z72mTP6Z4XyuG+Q==

"@types/webidl-conversions@*":
  version "7.0.3"
  resolved "https://registry.npmjs.org/@types/webidl-conversions/-/webidl-conversions-7.0.3.tgz"
  integrity sha512-CiJJvcRtIgzadHCYXw7dqEnMNRjhGZlYK05Mj9OyktqV8uVT8fD2BFOB7S1uwBE3Kj2Z+4UyPmFw/Ixgw/LAlA==

"@types/whatwg-url@^11.0.2":
  version "11.0.5"
  resolved "https://registry.npmjs.org/@types/whatwg-url/-/whatwg-url-11.0.5.tgz"
  integrity sha512-coYR071JRaHa+xoEvvYqvnIHaVqaYrLPbsufM9BF63HkwI5Lgmy2QR8Q5K/lYDYo5AK82wOvSOS0UsLTpTG7uQ==
  dependencies:
    "@types/webidl-conversions" "*"

abbrev@1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/abbrev/-/abbrev-1.1.1.tgz"
  integrity sha512-nne9/IiQ/hzIhY6pdDnbBtz7DjPTKrY00P/zvPSm5pOFkl6xuGrGnXn/VtTNNfNtAfZ9/1RtehkszU9qcTii0Q==

acorn-walk@^8.1.1:
  version "8.3.4"
  resolved "https://registry.npmjs.org/acorn-walk/-/acorn-walk-8.3.4.tgz"
  integrity sha512-ueEepnujpqee2o5aIYnvHU6C0A42MNdsIDeqy5BydrkuC5R1ZuUFnm27EeFJGoEHJQgn3uleRvmTXaJgfXbt4g==
  dependencies:
    acorn "^8.11.0"

acorn@^8.11.0, acorn@^8.4.1:
  version "8.14.1"
  resolved "https://registry.npmjs.org/acorn/-/acorn-8.14.1.tgz"
  integrity sha512-OvQ/2pUDKmgfCg++xsTX1wGxfTaszcHVcTctW4UJB4hibJx2HXxxO5UmVgyjMa+ZDsiaf5wWLXYpRWMmBI0QHg==

agent-base@6:
  version "6.0.2"
  resolved "https://registry.npmjs.org/agent-base/-/agent-base-6.0.2.tgz"
  integrity sha512-RZNwNclF7+MS/8bDg70amg32dyeZGZxiDuQmZxKLAlQjr3jGyLx+4Kkk58UO7D2QdgFIQCovuSuZESne6RG6XQ==
  dependencies:
    debug "4"

ansi-escapes@^4.3.2:
  version "4.3.2"
  resolved "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-4.3.2.tgz"
  integrity sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ==
  dependencies:
    type-fest "^0.21.3"

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz"
  integrity sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==

ansi-styles@^4.0.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz"
  integrity sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==
  dependencies:
    color-convert "^2.0.1"

"aproba@^1.0.3 || ^2.0.0":
  version "2.0.0"
  resolved "https://registry.npmjs.org/aproba/-/aproba-2.0.0.tgz"
  integrity sha512-lYe4Gx7QT+MKGbDsA+Z+he/Wtef0BiwDOlK/XkBrdfsh9J/jPPXbX0tE9x9cl27Tmu5gg3QUbUrQYa/y+KOHPQ==

are-we-there-yet@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/are-we-there-yet/-/are-we-there-yet-2.0.0.tgz"
  integrity sha512-Ci/qENmwHnsYo9xKIcUJN5LeDKdJ6R1Z1j9V/J5wyq8nh/mYPEpIKJbBZXtZjG04HiK7zV/p6Vs9952MrMeUIw==
  dependencies:
    delegates "^1.0.0"
    readable-stream "^3.6.0"

arg@^4.1.0:
  version "4.1.3"
  resolved "https://registry.npmjs.org/arg/-/arg-4.1.3.tgz"
  integrity sha512-58S9QDqG0Xx27YwPSt9fJxivjYl432YCwfDMfZ+71RAqUrZef7LrKQZ3LHLOwCS4FLNBplP533Zx895SeOCHvA==

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz"
  integrity sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==

base64-js@^1.0.2:
  version "1.5.1"
  resolved "https://registry.yarnpkg.com/base64-js/-/base64-js-1.5.1.tgz#1b1b440160a5bf7ad40b650f095963481903930a"
  integrity sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==

bcrypt@^5.1.1:
  version "5.1.1"
  resolved "https://registry.npmjs.org/bcrypt/-/bcrypt-5.1.1.tgz"
  integrity sha512-AGBHOG5hPYZ5Xl9KXzU5iKq9516yEmvCKDg3ecP5kX2aB6UqTeXZxk2ELnDgDm6BQSMlLt9rDB4LoSMx0rYwww==
  dependencies:
    "@mapbox/node-pre-gyp" "^1.0.11"
    node-addon-api "^5.0.0"

bowser@^2.11.0:
  version "2.12.1"
  resolved "https://registry.yarnpkg.com/bowser/-/bowser-2.12.1.tgz#f9ad78d7aebc472feb63dd9635e3ce2337e0e2c1"
  integrity sha512-z4rE2Gxh7tvshQ4hluIT7XcFrgLIQaw9X3A+kTTRdovCz5PMukm/0QC/BKSYPj3omF5Qfypn9O/c5kgpmvYUCw==

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz"
  integrity sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

bson@^6.10.3:
  version "6.10.3"
  resolved "https://registry.npmjs.org/bson/-/bson-6.10.3.tgz"
  integrity sha512-MTxGsqgYTwfshYWTRdmZRC+M7FnG1b4y7RO7p2k3X24Wq0yv1m77Wsj0BzlPzd/IowgESfsruQCUToa7vbOpPQ==

buffer@5.6.0:
  version "5.6.0"
  resolved "https://registry.yarnpkg.com/buffer/-/buffer-5.6.0.tgz#a31749dc7d81d84db08abf937b6b8c4033f62786"
  integrity sha512-/gDYp/UtU0eA1ys8bOs9J6a+E/KWIY+DZ+Q2WESNUA0jFRsJOc0SNUO6xJ5SGA1xueg3NL65W6s+NY5l9cunuw==
  dependencies:
    base64-js "^1.0.2"
    ieee754 "^1.1.4"

chalk@^5.4.1:
  version "5.4.1"
  resolved "https://registry.npmjs.org/chalk/-/chalk-5.4.1.tgz"
  integrity sha512-zgVZuo2WcZgfUEmsn6eO3kINexW8RAE4maiQ8QNs8CtpPCSyMiYsULR3HQYkm3w8FIA3SberyMJMSldGsW+U3w==

chardet@^0.7.0:
  version "0.7.0"
  resolved "https://registry.npmjs.org/chardet/-/chardet-0.7.0.tgz"
  integrity sha512-mT8iDcrh03qDGRRmoA2hmBJnxpllMR+0/0qlzjqZES6NdiWDcZkCNAk4rPFZ9Q85r27unkiNNg8ZOiwZXBHwcA==

chownr@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/chownr/-/chownr-2.0.0.tgz"
  integrity sha512-bIomtDF5KGpdogkLd9VspvFzk9KfpyyGlS8YFVZl7TGPBHL5snIOnxeshwVgPteQ9b4Eydl+pVbIyE1DcvCWgQ==

cli-width@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/cli-width/-/cli-width-4.1.0.tgz"
  integrity sha512-ouuZd4/dm2Sw5Gmqy6bGyNNNe1qt9RpmxveLSO7KcgsTnU7RXfsw+/bukWGo1abgBiMAic068rclZsO4IWmmxQ==

color-convert@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz"
  integrity sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==
  dependencies:
    color-name "~1.1.4"

color-name@~1.1.4:
  version "1.1.4"
  resolved "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz"
  integrity sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==

color-support@^1.1.2:
  version "1.1.3"
  resolved "https://registry.npmjs.org/color-support/-/color-support-1.1.3.tgz"
  integrity sha512-qiBjkpbMLO/HL68y+lh4q0/O1MZFj2RX6X/KmMa3+gJD3z+WwI1ZzDHysvqHGS3mP6mznPckpXmw1nI9cJjyRg==

commander@^13.1.0:
  version "13.1.0"
  resolved "https://registry.npmjs.org/commander/-/commander-13.1.0.tgz"
  integrity sha512-/rFeCpNJQbhSZjGVwO9RFV3xPqbnERS8MmIQzCtD/zl6gpJuV/bMLuN92oG3F7d8oDEHHRrujSXNUr8fpjntKw==

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz"
  integrity sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==

console-control-strings@^1.0.0, console-control-strings@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/console-control-strings/-/console-control-strings-1.1.0.tgz"
  integrity sha512-ty/fTekppD2fIwRvnZAVdeOiGd1c7YXEixbgJTNzqcxJWKQnjJ/V1bNEEE6hygpM3WjwHFUVK6HTjWSzV4a8sQ==

create-require@^1.1.0:
  version "1.1.1"
  resolved "https://registry.npmjs.org/create-require/-/create-require-1.1.1.tgz"
  integrity sha512-dcKFX3jn0MpIaXjisoRvexIJVEKzaq7z2rZKxf+MSr9TkdmHmsU4m2lcLojrj/FHl8mk5VxMmYA+ftRkP/3oKQ==

csv-parse@^5.6.0:
  version "5.6.0"
  resolved "https://registry.yarnpkg.com/csv-parse/-/csv-parse-5.6.0.tgz#219beace2a3e9f28929999d2aa417d3fb3071c7f"
  integrity sha512-l3nz3euub2QMg5ouu5U09Ew9Wf6/wQ8I++ch1loQ0ljmzhmfZYrH9fflS22i/PQEvsPvxCwxgz5q7UB8K1JO4Q==

date-format@^4.0.14:
  version "4.0.14"
  resolved "https://registry.npmjs.org/date-format/-/date-format-4.0.14.tgz"
  integrity sha512-39BOQLs9ZjKh0/patS9nrT8wc3ioX3/eA/zgbKNopnF2wCqJEoxywwwElATYvRsXdnOxA/OQeQoFZ3rFjVajhg==

debug@4, debug@4.x, debug@^4.3.4:
  version "4.4.0"
  resolved "https://registry.npmjs.org/debug/-/debug-4.4.0.tgz"
  integrity sha512-6WTZ/IxCY/T6BALoZHaE4ctp9xm+Z5kY/pzYaCHRFeyVhojxlrm+46y68HA6hr0TcwEssoxNiDEUJQjfPZ/RYA==
  dependencies:
    ms "^2.1.3"

delegates@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/delegates/-/delegates-1.0.0.tgz"
  integrity sha512-bd2L678uiWATM6m5Z1VzNCErI3jiGzt6HGY8OVICs40JQq/HALfbyNJmp0UDakEY4pMMaN0Ly5om/B1VI/+xfQ==

detect-libc@^2.0.0:
  version "2.0.4"
  resolved "https://registry.npmjs.org/detect-libc/-/detect-libc-2.0.4.tgz"
  integrity sha512-3UDv+G9CsCKO1WKMGw9fwq/SWJYbI0c5Y7LU1AXYoDdbhE2AHQ6N6Nb34sG8Fj7T5APy8qXDCKuuIHd1BR0tVA==

diff@^4.0.1:
  version "4.0.2"
  resolved "https://registry.npmjs.org/diff/-/diff-4.0.2.tgz"
  integrity sha512-58lmxKSA4BNyLz+HHMUzlOEpg09FV+ev6ZMe3vJihgdxzgcwZ8VoEEPmALCZG9LmqfVoNMMKpttIYTVG6uDY7A==

dotenv@^16.4.7:
  version "16.4.7"
  resolved "https://registry.npmjs.org/dotenv/-/dotenv-16.4.7.tgz"
  integrity sha512-47qPchRCykZC03FhkYAhrvwU4xDBFIj1QPqaarj6mdM/hgUzfPHcpkHJOn3mJAufFeeAxAzeGsr5X0M4k6fLZQ==

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz"
  integrity sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==

events@3.3.0:
  version "3.3.0"
  resolved "https://registry.yarnpkg.com/events/-/events-3.3.0.tgz#31a95ad0a924e2d2c419a813aeb2c4e878ea7400"
  integrity sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==

external-editor@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/external-editor/-/external-editor-3.1.0.tgz"
  integrity sha512-hMQ4CX1p1izmuLYyZqLMO/qGNw10wSv9QDCPfzXfyFrOaCSSoRfqE1Kf1s5an66J5JZC62NewG+mK49jOCtQew==
  dependencies:
    chardet "^0.7.0"
    iconv-lite "^0.4.24"
    tmp "^0.0.33"

fast-xml-parser@5.2.5:
  version "5.2.5"
  resolved "https://registry.yarnpkg.com/fast-xml-parser/-/fast-xml-parser-5.2.5.tgz#4809fdfb1310494e341098c25cb1341a01a9144a"
  integrity sha512-pfX9uG9Ki0yekDHx2SiuRIyFdyAr1kMIMitPvb0YBo8SUfKvia7w7FIyd/l6av85pFYRhZscS75MwMnbvY+hcQ==
  dependencies:
    strnum "^2.1.0"

flatted@^3.2.7:
  version "3.3.3"
  resolved "https://registry.npmjs.org/flatted/-/flatted-3.3.3.tgz"
  integrity sha512-GX+ysw4PBCz0PzosHDepZGANEuFCMLrnRTiEy9McGjmkCQYwRq4A/X786G/fjM/+OjsWSU1ZrY5qyARZmO/uwg==

fs-extra@^8.1.0:
  version "8.1.0"
  resolved "https://registry.npmjs.org/fs-extra/-/fs-extra-8.1.0.tgz"
  integrity sha512-yhlQgA6mnOJUKOsRUFsgJdQCvkKhcz8tlZG5HBQfReYZy46OwLcY+Zia0mtdHsOo9y/hP+CxMN0TU9QxoOtG4g==
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^4.0.0"
    universalify "^0.1.0"

fs-minipass@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/fs-minipass/-/fs-minipass-2.1.0.tgz"
  integrity sha512-V/JgOLFCS+R6Vcq0slCuaeWEdNC3ouDlJMNIsacH2VtALiu9mV4LPrHc5cDl8k5aw6J8jwgWWpiTo5RYhmIzvg==
  dependencies:
    minipass "^3.0.0"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz"
  integrity sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==

gauge@^3.0.0:
  version "3.0.2"
  resolved "https://registry.npmjs.org/gauge/-/gauge-3.0.2.tgz"
  integrity sha512-+5J6MS/5XksCuXq++uFRsnUd7Ovu1XenbeuIuNRJxYWjgQbPuFhT14lAvsWfqfAmnwluf1OwMjz39HjfLPci0Q==
  dependencies:
    aproba "^1.0.3 || ^2.0.0"
    color-support "^1.1.2"
    console-control-strings "^1.0.0"
    has-unicode "^2.0.1"
    object-assign "^4.1.1"
    signal-exit "^3.0.0"
    string-width "^4.2.3"
    strip-ansi "^6.0.1"
    wide-align "^1.1.2"

glob@^7.1.3:
  version "7.2.3"
  resolved "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz"
  integrity sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

graceful-fs@^4.1.6, graceful-fs@^4.2.0:
  version "4.2.11"
  resolved "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz"
  integrity sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==

has-unicode@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/has-unicode/-/has-unicode-2.0.1.tgz"
  integrity sha512-8Rf9Y83NBReMnx0gFzA8JImQACstCYWUplepDa9xprwwtmgEZUF0h/i5xSA625zB/I37EtrswSST6OXxwaaIJQ==

https-proxy-agent@^5.0.0:
  version "5.0.1"
  resolved "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-5.0.1.tgz"
  integrity sha512-dFcAjpTQFgoLMzC2VwU+C/CbS7uRL0lWmxDITmqm7C+7F0Odmj6s9l6alZc6AELXhrnggM2CeWSXHGOdX2YtwA==
  dependencies:
    agent-base "6"
    debug "4"

iconv-lite@^0.4.24:
  version "0.4.24"
  resolved "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz"
  integrity sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

ieee754@^1.1.4:
  version "1.2.1"
  resolved "https://registry.yarnpkg.com/ieee754/-/ieee754-1.2.1.tgz#8eb7a10a63fff25d15a57b001586d177d1b0d352"
  integrity sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz"
  integrity sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@^2.0.3, inherits@~2.0.4:
  version "2.0.4"
  resolved "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz"
  integrity sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==

inquirer@^12.6.0:
  version "12.6.0"
  resolved "https://registry.npmjs.org/inquirer/-/inquirer-12.6.0.tgz"
  integrity sha512-3zmmccQd/8o65nPOZJZ+2wqt76Ghw3+LaMrmc6JE/IzcvQhJ1st+QLCOo/iLS85/tILU0myG31a2TAZX0ysAvg==
  dependencies:
    "@inquirer/core" "^10.1.10"
    "@inquirer/prompts" "^7.5.0"
    "@inquirer/type" "^3.0.6"
    ansi-escapes "^4.3.2"
    mute-stream "^2.0.0"
    run-async "^3.0.0"
    rxjs "^7.8.2"

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz"
  integrity sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==

jsonfile@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/jsonfile/-/jsonfile-4.0.0.tgz"
  integrity sha512-m6F1R3z8jjlf2imQHS2Qez5sjKWQzbuuhuJ/FKYFRZvPE3PuHcSMVZzfsLhGVOkfd20obL5SWEBew5ShlquNxg==
  optionalDependencies:
    graceful-fs "^4.1.6"

kareem@2.6.3:
  version "2.6.3"
  resolved "https://registry.npmjs.org/kareem/-/kareem-2.6.3.tgz"
  integrity sha512-C3iHfuGUXK2u8/ipq9LfjFfXFxAZMQJJq7vLS45r3D9Y2xQ/m4S8zaR4zMLFWh9AsNPXmcFfUDhTEO8UIC/V6Q==

log4js@^6.9.1:
  version "6.9.1"
  resolved "https://registry.npmjs.org/log4js/-/log4js-6.9.1.tgz"
  integrity sha512-1somDdy9sChrr9/f4UlzhdaGfDR2c/SaD2a4T7qEkG4jTS57/B3qmnjLYePwQ8cqWnUHZI0iAKxMBpCZICiZ2g==
  dependencies:
    date-format "^4.0.14"
    debug "^4.3.4"
    flatted "^3.2.7"
    rfdc "^1.3.0"
    streamroller "^3.1.5"

make-dir@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/make-dir/-/make-dir-3.1.0.tgz"
  integrity sha512-g3FeP20LNwhALb/6Cz6Dd4F2ngze0jz7tbzrD2wAV+o9FeNHe4rL+yK2md0J/fiSf1sa1ADhXqi5+oVwOM/eGw==
  dependencies:
    semver "^6.0.0"

make-error@^1.1.1:
  version "1.3.6"
  resolved "https://registry.npmjs.org/make-error/-/make-error-1.3.6.tgz"
  integrity sha512-s8UhlNe7vPKomQhC1qFelMokr/Sc3AgNbso3n74mVPA5LTZwkB9NlXf4XPamLxJE8h0gh73rM94xvwRT2CVInw==

memory-pager@^1.0.2:
  version "1.5.0"
  resolved "https://registry.npmjs.org/memory-pager/-/memory-pager-1.5.0.tgz"
  integrity sha512-ZS4Bp4r/Zoeq6+NLJpP+0Zzm0pR8whtGPf1XExKLJBAczGMnSi3It14OiNCStjQjM6NU1okjQGSxgEZN8eBYKg==

mime-db@^1.54.0:
  version "1.54.0"
  resolved "https://registry.yarnpkg.com/mime-db/-/mime-db-1.54.0.tgz#cddb3ee4f9c64530dff640236661d42cb6a314f5"
  integrity sha512-aU5EJuIN2WDemCcAp2vFBfp/m4EAhWJnUNSSw0ixs7/kXbd6Pg64EmwJkNdFhB8aWt1sH2CTXrLxo/iAGV3oPQ==

mime-types@^3.0.1:
  version "3.0.1"
  resolved "https://registry.yarnpkg.com/mime-types/-/mime-types-3.0.1.tgz#b1d94d6997a9b32fd69ebaed0db73de8acb519ce"
  integrity sha512-xRc4oEhT6eaBpU1XF7AjpOFD+xQmXNB5OVKwp4tqCuBpHLS/ZbBDrc07mYTDqVMg6PfxUjjNp85O6Cd2Z/5HWA==
  dependencies:
    mime-db "^1.54.0"

minimatch@^3.1.1:
  version "3.1.2"
  resolved "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz"
  integrity sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==
  dependencies:
    brace-expansion "^1.1.7"

minipass@^3.0.0:
  version "3.3.6"
  resolved "https://registry.npmjs.org/minipass/-/minipass-3.3.6.tgz"
  integrity sha512-DxiNidxSEK+tHG6zOIklvNOwm3hvCrbUrdtzY74U6HKTJxvIDfOUL5W5P2Ghd3DTkhhKPYGqeNUIh5qcM4YBfw==
  dependencies:
    yallist "^4.0.0"

minipass@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/minipass/-/minipass-5.0.0.tgz"
  integrity sha512-3FnjYuehv9k6ovOEbyOswadCDPX1piCfhV8ncmYtHOjuPwylVWsghTLo7rabjC3Rx5xD4HDx8Wm1xnMF7S5qFQ==

minizlib@^2.1.1:
  version "2.1.2"
  resolved "https://registry.npmjs.org/minizlib/-/minizlib-2.1.2.tgz"
  integrity sha512-bAxsR8BVfj60DWXHE3u30oHzfl4G7khkSuPW+qvpd7jFRHm7dLxOjUk1EHACJ/hxLY8phGJ0YhYHZo7jil7Qdg==
  dependencies:
    minipass "^3.0.0"
    yallist "^4.0.0"

mkdirp@^1.0.3:
  version "1.0.4"
  resolved "https://registry.npmjs.org/mkdirp/-/mkdirp-1.0.4.tgz"
  integrity sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==

moment@^2.30.1:
  version "2.30.1"
  resolved "https://registry.yarnpkg.com/moment/-/moment-2.30.1.tgz#f8c91c07b7a786e30c59926df530b4eac96974ae"
  integrity sha512-uEmtNhbDOrWPFS+hdjFCBfy9f2YoyzRpwcl+DqpC6taX21FzsTLQVbMV/W7PzNSX6x/bhC1zA3c2UQ5NzH6how==

mongodb-connection-string-url@^3.0.0:
  version "3.0.2"
  resolved "https://registry.npmjs.org/mongodb-connection-string-url/-/mongodb-connection-string-url-3.0.2.tgz"
  integrity sha512-rMO7CGo/9BFwyZABcKAWL8UJwH/Kc2x0g72uhDWzG48URRax5TCIcJ7Rc3RZqffZzO/Gwff/jyKwCU9TN8gehA==
  dependencies:
    "@types/whatwg-url" "^11.0.2"
    whatwg-url "^14.1.0 || ^13.0.0"

mongodb@~6.14.0:
  version "6.14.2"
  resolved "https://registry.npmjs.org/mongodb/-/mongodb-6.14.2.tgz"
  integrity sha512-kMEHNo0F3P6QKDq17zcDuPeaywK/YaJVCEQRzPF3TOM/Bl9MFg64YE5Tu7ifj37qZJMhwU1tl2Ioivws5gRG5Q==
  dependencies:
    "@mongodb-js/saslprep" "^1.1.9"
    bson "^6.10.3"
    mongodb-connection-string-url "^3.0.0"

mongoose@^8.12.1:
  version "8.12.1"
  resolved "https://registry.npmjs.org/mongoose/-/mongoose-8.12.1.tgz"
  integrity sha512-UW22y8QFVYmrb36hm8cGncfn4ARc/XsYWQwRTaj0gxtQk1rDuhzDO1eBantS+hTTatfAIS96LlRCJrcNHvW5+Q==
  dependencies:
    bson "^6.10.3"
    kareem "2.6.3"
    mongodb "~6.14.0"
    mpath "0.9.0"
    mquery "5.0.0"
    ms "2.1.3"
    sift "17.1.3"

mpath@0.9.0:
  version "0.9.0"
  resolved "https://registry.npmjs.org/mpath/-/mpath-0.9.0.tgz"
  integrity sha512-ikJRQTk8hw5DEoFVxHG1Gn9T/xcjtdnOKIU1JTmGjZZlg9LST2mBLmcX3/ICIbgJydT2GOc15RnNy5mHmzfSew==

mquery@5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/mquery/-/mquery-5.0.0.tgz"
  integrity sha512-iQMncpmEK8R8ncT8HJGsGc9Dsp8xcgYMVSbs5jgnm1lFHTZqMJTUWTDx1LBO8+mK3tPNZWFLBghQEIOULSTHZg==
  dependencies:
    debug "4.x"

ms@2.1.3, ms@^2.1.3:
  version "2.1.3"
  resolved "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz"
  integrity sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==

mute-stream@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/mute-stream/-/mute-stream-2.0.0.tgz"
  integrity sha512-WWdIxpyjEn+FhQJQQv9aQAYlHoNVdzIzUySNV1gHUPDSdZJ3yZn7pAAbQcV7B56Mvu881q9FZV+0Vx2xC44VWA==

node-addon-api@^5.0.0:
  version "5.1.0"
  resolved "https://registry.npmjs.org/node-addon-api/-/node-addon-api-5.1.0.tgz"
  integrity sha512-eh0GgfEkpnoWDq+VY8OyvYhFEzBk6jIYbRKdIlyTiAXIVJ8PyBaKb0rp7oDtoddbdoHWhq8wwr+XZ81F1rpNdA==

node-fetch@^2.6.7:
  version "2.7.0"
  resolved "https://registry.npmjs.org/node-fetch/-/node-fetch-2.7.0.tgz"
  integrity sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==
  dependencies:
    whatwg-url "^5.0.0"

nopt@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/nopt/-/nopt-5.0.0.tgz"
  integrity sha512-Tbj67rffqceeLpcRXrT7vKAN8CwfPeIBgM7E6iBkmKLV7bEMwpGgYLGv0jACUsECaa/vuxP0IjEont6umdMgtQ==
  dependencies:
    abbrev "1"

npmlog@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/npmlog/-/npmlog-5.0.1.tgz"
  integrity sha512-AqZtDUWOMKs1G/8lwylVjrdYgqA4d9nu8hc+0gzRxlDb1I10+FHBGMXs6aiQHFdCUUlqH99MUMuLfzWDNDtfxw==
  dependencies:
    are-we-there-yet "^2.0.0"
    console-control-strings "^1.1.0"
    gauge "^3.0.0"
    set-blocking "^2.0.0"

number-to-words@^1.2.4:
  version "1.2.4"
  resolved "https://registry.npmjs.org/number-to-words/-/number-to-words-1.2.4.tgz"
  integrity sha512-/fYevVkXRcyBiZDg6yzZbm0RuaD6i0qRfn8yr+6D0KgBMOndFPxuW10qCHpzs50nN8qKuv78k8MuotZhcVX6Pw==

object-assign@^4.1.1:
  version "4.1.1"
  resolved "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz"
  integrity sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==

once@^1.3.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/once/-/once-1.4.0.tgz"
  integrity sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==
  dependencies:
    wrappy "1"

os-tmpdir@~1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/os-tmpdir/-/os-tmpdir-1.0.2.tgz"
  integrity sha512-D2FR03Vir7FIu45XBY20mTb+/ZSWB00sjU9jdQXt83gDrI4Ztz5Fs7/yy74g2N5SVQY4xY1qDr4rNddwYRVX0g==

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz"
  integrity sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==

punycode@^2.3.1:
  version "2.3.1"
  resolved "https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz"
  integrity sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==

readable-stream@^3.5.0, readable-stream@^3.6.0:
  version "3.6.2"
  resolved "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz"
  integrity sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

rfdc@^1.3.0:
  version "1.4.1"
  resolved "https://registry.npmjs.org/rfdc/-/rfdc-1.4.1.tgz"
  integrity sha512-q1b3N5QkRUWUl7iyylaaj3kOpIT0N2i9MqIEQXP73GVsN9cw3fdx8X63cEmWhJGi2PPCF23Ijp7ktmd39rawIA==

rimraf@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npmjs.org/rimraf/-/rimraf-3.0.2.tgz"
  integrity sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==
  dependencies:
    glob "^7.1.3"

run-async@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/run-async/-/run-async-3.0.0.tgz"
  integrity sha512-540WwVDOMxA6dN6We19EcT9sc3hkXPw5mzRNGM3FkdN/vtE9NFvj5lFAPNwUDmJjXidm3v7TC1cTE7t17Ulm1Q==

rxjs@^7.8.2:
  version "7.8.2"
  resolved "https://registry.npmjs.org/rxjs/-/rxjs-7.8.2.tgz"
  integrity sha512-dhKf903U/PQZY6boNNtAGdWbG85WAbjT/1xYoZIC7FAY0yWapOBQVsVrDl58W86//e1VpMNBtRV4MaXfdMySFA==
  dependencies:
    tslib "^2.1.0"

safe-buffer@~5.2.0:
  version "5.2.1"
  resolved "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz"
  integrity sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==

"safer-buffer@>= 2.1.2 < 3":
  version "2.1.2"
  resolved "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz"
  integrity sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==

semver@^6.0.0:
  version "6.3.1"
  resolved "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz"
  integrity sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==

semver@^7.3.5:
  version "7.7.1"
  resolved "https://registry.npmjs.org/semver/-/semver-7.7.1.tgz"
  integrity sha512-hlq8tAfn0m/61p4BVRcPzIGr6LKiMwo4VM6dGi6pt4qcRkmNzTcWq6eCEjEh+qXjkMDvPlOFFSGwQjoEa6gyMA==

set-blocking@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/set-blocking/-/set-blocking-2.0.0.tgz"
  integrity sha512-KiKBS8AnWGEyLzofFfmvKwpdPzqiy16LvQfK3yv/fVH7Bj13/wl3JSR1J+rfgRE9q7xUJK4qvgS8raSOeLUehw==

sift@17.1.3:
  version "17.1.3"
  resolved "https://registry.npmjs.org/sift/-/sift-17.1.3.tgz"
  integrity sha512-Rtlj66/b0ICeFzYTuNvX/EF1igRbbnGSvEyT79McoZa/DeGhMyC5pWKOEsZKnpkqtSeovd5FL/bjHWC3CIIvCQ==

signal-exit@^3.0.0:
  version "3.0.7"
  resolved "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz"
  integrity sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==

signal-exit@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/signal-exit/-/signal-exit-4.1.0.tgz"
  integrity sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==

sparse-bitfield@^3.0.3:
  version "3.0.3"
  resolved "https://registry.npmjs.org/sparse-bitfield/-/sparse-bitfield-3.0.3.tgz"
  integrity sha512-kvzhi7vqKTfkh0PZU+2D2PIllw2ymqJKujUcyPMd9Y75Nv4nPbGJZXNhxsgdQab2BmlDct1YnfQCguEvHr7VsQ==
  dependencies:
    memory-pager "^1.0.2"

stream-browserify@3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/stream-browserify/-/stream-browserify-3.0.0.tgz#22b0a2850cdf6503e73085da1fc7b7d0c2122f2f"
  integrity sha512-H73RAHsVBapbim0tU2JwwOiXUj+fikfiaoYAKHF3VJfA0pe2BCzkhAHBlLG6REzE+2WNZcxOXjK7lkso+9euLA==
  dependencies:
    inherits "~2.0.4"
    readable-stream "^3.5.0"

streamroller@^3.1.5:
  version "3.1.5"
  resolved "https://registry.npmjs.org/streamroller/-/streamroller-3.1.5.tgz"
  integrity sha512-KFxaM7XT+irxvdqSP1LGLgNWbYN7ay5owZ3r/8t77p+EtSUAfUgtl7be3xtqtOmGUl9K9YPO2ca8133RlTjvKw==
  dependencies:
    date-format "^4.0.14"
    debug "^4.3.4"
    fs-extra "^8.1.0"

"string-width@^1.0.2 || 2 || 3 || 4", string-width@^4.1.0, string-width@^4.2.3:
  version "4.2.3"
  resolved "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz"
  integrity sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string_decoder@^1.1.1:
  version "1.3.0"
  resolved "https://registry.npmjs.org/string_decoder/-/string_decoder-1.3.0.tgz"
  integrity sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==
  dependencies:
    safe-buffer "~5.2.0"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz"
  integrity sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==
  dependencies:
    ansi-regex "^5.0.1"

strnum@^2.1.0:
  version "2.1.1"
  resolved "https://registry.yarnpkg.com/strnum/-/strnum-2.1.1.tgz#cf2a6e0cf903728b8b2c4b971b7e36b4e82d46ab"
  integrity sha512-7ZvoFTiCnGxBtDqJ//Cu6fWtZtc7Y3x+QOirG15wztbdngGSkht27o2pyGWrVy0b4WAy3jbKmnoK6g5VlVNUUw==

tar@^6.1.11:
  version "6.2.1"
  resolved "https://registry.npmjs.org/tar/-/tar-6.2.1.tgz"
  integrity sha512-DZ4yORTwrbTj/7MZYq2w+/ZFdI6OZ/f9SFHR+71gIVUZhOQPHzVCLpvRnPgyaMpfWxxk/4ONva3GQSyNIKRv6A==
  dependencies:
    chownr "^2.0.0"
    fs-minipass "^2.0.0"
    minipass "^5.0.0"
    minizlib "^2.1.1"
    mkdirp "^1.0.3"
    yallist "^4.0.0"

tmp@^0.0.33:
  version "0.0.33"
  resolved "https://registry.npmjs.org/tmp/-/tmp-0.0.33.tgz"
  integrity sha512-jRCJlojKnZ3addtTOjdIqoRuPEKBvNXcGYqzO6zWZX8KfKEpnGY5jfggJQ3EjKuu8D4bJRr0y+cYJFmYbImXGw==
  dependencies:
    os-tmpdir "~1.0.2"

tr46@^5.1.0:
  version "5.1.0"
  resolved "https://registry.npmjs.org/tr46/-/tr46-5.1.0.tgz"
  integrity sha512-IUWnUK7ADYR5Sl1fZlO1INDUhVhatWl7BtJWsIhwJ0UAK7ilzzIa8uIqOO/aYVWHZPJkKbEL+362wrzoeRF7bw==
  dependencies:
    punycode "^2.3.1"

tr46@~0.0.3:
  version "0.0.3"
  resolved "https://registry.npmjs.org/tr46/-/tr46-0.0.3.tgz"
  integrity sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==

ts-node@^10.9.2:
  version "10.9.2"
  resolved "https://registry.npmjs.org/ts-node/-/ts-node-10.9.2.tgz"
  integrity sha512-f0FFpIdcHgn8zcPSbf1dRevwt047YMnaiJM3u2w2RewrB+fob/zePZcrOyQoLMMO7aBIddLcQIEK5dYjkLnGrQ==
  dependencies:
    "@cspotcode/source-map-support" "^0.8.0"
    "@tsconfig/node10" "^1.0.7"
    "@tsconfig/node12" "^1.0.7"
    "@tsconfig/node14" "^1.0.0"
    "@tsconfig/node16" "^1.0.2"
    acorn "^8.4.1"
    acorn-walk "^8.1.1"
    arg "^4.1.0"
    create-require "^1.1.0"
    diff "^4.0.1"
    make-error "^1.1.1"
    v8-compile-cache-lib "^3.0.1"
    yn "3.1.1"

tslib@^2.1.0, tslib@^2.6.2:
  version "2.8.1"
  resolved "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz"
  integrity sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==

type-fest@^0.21.3:
  version "0.21.3"
  resolved "https://registry.npmjs.org/type-fest/-/type-fest-0.21.3.tgz"
  integrity sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w==

typescript@^5.8.2:
  version "5.8.2"
  resolved "https://registry.npmjs.org/typescript/-/typescript-5.8.2.tgz"
  integrity sha512-aJn6wq13/afZp/jT9QZmwEjDqqvSGp1VT5GVg+f/t6/oVyrgXM6BY1h9BRh/O5p3PlUPAe+WuiEZOmb/49RqoQ==

undici-types@~6.20.0:
  version "6.20.0"
  resolved "https://registry.npmjs.org/undici-types/-/undici-types-6.20.0.tgz"
  integrity sha512-Ny6QZ2Nju20vw1SRHe3d9jVu6gJ+4e3+MMpqu7pqE5HT6WsTSlce++GQmK5UXS8mzV8DSYHrQH+Xrf2jVcuKNg==

universalify@^0.1.0:
  version "0.1.2"
  resolved "https://registry.npmjs.org/universalify/-/universalify-0.1.2.tgz"
  integrity sha512-rBJeI5CXAlmy1pV+617WB9J63U6XcazHHF2f2dbJix4XzpUF0RS3Zbj0FGIOCAva5P/d/GBOYaACQ1w+0azUkg==

util-deprecate@^1.0.1:
  version "1.0.2"
  resolved "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz"
  integrity sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==

uuid4@^2.0.3:
  version "2.0.3"
  resolved "https://registry.yarnpkg.com/uuid4/-/uuid4-2.0.3.tgz#241e5dfe1704a79c52e2aa40e7e581a5e7b01ab4"
  integrity sha512-CTpAkEVXMNJl2ojgtpLXHgz23dh8z81u6/HEPiQFOvBc/c2pde6TVHmH4uwY0d/GLF3tb7+VDAj4+2eJaQSdZQ==

uuid@^9.0.1:
  version "9.0.1"
  resolved "https://registry.yarnpkg.com/uuid/-/uuid-9.0.1.tgz#e188d4c8853cc722220392c424cd637f32293f30"
  integrity sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==

v8-compile-cache-lib@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/v8-compile-cache-lib/-/v8-compile-cache-lib-3.0.1.tgz"
  integrity sha512-wa7YjyUGfNZngI/vtK0UHAN+lgDCxBPCylVXGp0zu59Fz5aiGtNXaq3DhIov063MorB+VfufLh3JlF2KdTK3xg==

validator@^13.15.15:
  version "13.15.15"
  resolved "https://registry.yarnpkg.com/validator/-/validator-13.15.15.tgz#246594be5671dc09daa35caec5689fcd18c6e7e4"
  integrity sha512-BgWVbCI72aIQy937xbawcs+hrVaN/CZ2UwutgaJ36hGqRrLNM+f5LUT/YPRbo8IV/ASeFzXszezV+y2+rq3l8A==

webidl-conversions@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-3.0.1.tgz"
  integrity sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==

webidl-conversions@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-7.0.0.tgz"
  integrity sha512-VwddBukDzu71offAQR975unBIGqfKZpM+8ZX6ySk8nYhVoo5CYaZyzt3YBvYtRtO+aoGlqxPg/B87NGVZ/fu6g==

"whatwg-url@^14.1.0 || ^13.0.0":
  version "14.2.0"
  resolved "https://registry.npmjs.org/whatwg-url/-/whatwg-url-14.2.0.tgz"
  integrity sha512-De72GdQZzNTUBBChsXueQUnPKDkg/5A5zp7pFDuQAj5UFoENpiACU0wlCvzpAGnTkj++ihpKwKyYewn/XNUbKw==
  dependencies:
    tr46 "^5.1.0"
    webidl-conversions "^7.0.0"

whatwg-url@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/whatwg-url/-/whatwg-url-5.0.0.tgz"
  integrity sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==
  dependencies:
    tr46 "~0.0.3"
    webidl-conversions "^3.0.0"

wide-align@^1.1.2:
  version "1.1.5"
  resolved "https://registry.npmjs.org/wide-align/-/wide-align-1.1.5.tgz"
  integrity sha512-eDMORYaPNZ4sQIuuYPDHdQvf4gyCF9rEEV/yPxGfwPkRodwEgiMUUXTx/dex+Me0wxx53S+NgUHaP7y3MGlDmg==
  dependencies:
    string-width "^1.0.2 || 2 || 3 || 4"

wrap-ansi@^6.2.0:
  version "6.2.0"
  resolved "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-6.2.0.tgz"
  integrity sha512-r6lPcBGxZXlIcymEu7InxDMhdW0KDxpLgoFLcguasxCaJ/SOIZwINatK9KY/tf+ZrlywOKU0UDj3ATXUBfxJXA==
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrappy@1:
  version "1.0.2"
  resolved "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz"
  integrity sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==

yallist@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/yallist/-/yallist-4.0.0.tgz"
  integrity sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==

yn@3.1.1:
  version "3.1.1"
  resolved "https://registry.npmjs.org/yn/-/yn-3.1.1.tgz"
  integrity sha512-Ux4ygGWsu2c7isFWe8Yu1YluJmqVhxqK2cLXNQA5AcC3QfbGNpM7fu0Y8b/z16pXLnFxZYvWhd3fhBY9DLmC6Q==

yoctocolors-cjs@^2.1.2:
  version "2.1.2"
  resolved "https://registry.npmjs.org/yoctocolors-cjs/-/yoctocolors-cjs-2.1.2.tgz"
  integrity sha512-cYVsTjKl8b+FrnidjibDWskAv7UKOfcwaVZdp/it9n1s9fU3IkgDbhdIRKCW4JDsAlECJY0ytoVPT3sK6kideA==
