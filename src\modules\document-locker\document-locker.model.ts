import mongoose, { Document, Schema, Types } from 'mongoose';

export interface IDocumentLocker extends Document {
  userId: Types.ObjectId;
  facilityId: Types.ObjectId;
  organizationId: Types.ObjectId;
  fileUrl: string;
  documentName: string;
  uploadedBy: Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

const DocumentLockerSchema = new Schema<IDocumentLocker>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  facilityId: {
    type: Schema.Types.ObjectId,
    ref: 'Facility',
    required: true,
    index: true
  },
  organizationId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  fileUrl: {
    type: String,
    required: true
  },
  documentName: {
    type: String,
    required: true
  },
  uploadedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  }
}, {
  timestamps: true
});

// Create indexes for better query performance
DocumentLockerSchema.index({ userId: 1, facilityId: 1 });
DocumentLockerSchema.index({ organizationId: 1, createdAt: -1 });

export const DOCUMENT_LOCKER_COLLECTION = 'documentLockers';
export const DocumentLocker = mongoose.model<IDocumentLocker>('DocumentLocker', DocumentLockerSchema, DOCUMENT_LOCKER_COLLECTION);
