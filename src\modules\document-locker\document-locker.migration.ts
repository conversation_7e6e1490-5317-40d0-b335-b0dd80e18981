import * as path from 'path';
import { Types } from 'mongoose';
import uuid4 from "uuid4";
import { connectToMongo, closeConnection } from '../../common/database/db.module';
import { parseCSVWithRowNumbers } from '../../common/utils/csv-parser';
import { DocumentLocker, IDocumentLocker } from './document-locker.model';
import { User } from '../user/user.model';
import { Client } from '../user/client.model';
import logger from '../../common/logger/log.module';
import { ENUM_ROLE_TYPE } from '../role/role.enum';
import { S3Service } from '../../common/aws/s3.service';

interface ICsvDocumentLocker {
  file_id: string;
  id: string;
  first_name: string;
  middle_name?: string;
  last_name: string;
  dob: string;
  path: string;
}

const CsvToObjectKeyMapDocumentLocker: Record<keyof ICsvDocumentLocker, string> = {
  file_id: "file id",
  id: "id",
  first_name: "first name",
  middle_name: "middle name",
  last_name: "last name",
  dob: "dob",
  path: "path"
};

/**
 * Validate document locker CSV data
 */
function validateDocumentLockerData(documentLocker: ICsvDocumentLocker, rowNumber: number): string[] {
  const errors: string[] = [];

  if (!documentLocker.file_id || typeof documentLocker.file_id !== 'string' || !documentLocker.file_id.trim()) {
    errors.push(`Row ${rowNumber}: file_id is required`);
  }

  if (!documentLocker.id || typeof documentLocker.id !== 'string' || !documentLocker.id.trim()) {
    errors.push(`Row ${rowNumber}: id is required`);
  }

  if (!documentLocker.first_name || typeof documentLocker.first_name !== 'string' || !documentLocker.first_name.trim()) {
    errors.push(`Row ${rowNumber}: first_name is required`);
  }

  if (!documentLocker.last_name || typeof documentLocker.last_name !== 'string' || !documentLocker.last_name.trim()) {
    errors.push(`Row ${rowNumber}: last_name is required`);
  }

  if (!documentLocker.path || typeof documentLocker.path !== 'string' || !documentLocker.path.trim()) {
    errors.push(`Row ${rowNumber}: path is required`);
  }

  return errors;
}

/**
 * Generate document name from file path
 */
function generateDocumentName(filePath: string, userId: string, firstName: string, lastName: string, middleName?: string): string {
  const fileName = path.basename(filePath);
  const nameWithoutExtension = path.parse(fileName).name;

  // If the filename already contains the name, use it as is
  if (nameWithoutExtension.includes(firstName) || nameWithoutExtension.includes(lastName)) {
    return nameWithoutExtension;
  }

  // Otherwise, create a name from the user details
  const fullName = middleName ? `${firstName} ${middleName} ${lastName}` : `${firstName} ${lastName}`;
  return `${fullName}-${userId}-${uuid4()}`;
}

/**
 * Migrate document locker data from CSV to MongoDB
 */
export async function migrateDocumentLocker(_dbName: string = 'hop-migration'): Promise<void> {
  let session: any = null;

  try {
    logger.log('Starting document locker migration...');

    // Connect to database
    const mongoose = await connectToMongo();

    // Start a session for transaction
    session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Get document locker data from CSV
      const documentLockers = await getDocumentLockers(session);

      if (!documentLockers || documentLockers.length === 0) {
        logger.log('No valid document lockers to migrate');
        return;
      }

      logger.log(`Successfully migrated ${documentLockers.length} document lockers to MongoDB`);

      // Commit the transaction
      await session.commitTransaction();
      logger.log('Transaction committed successfully');
    } catch (error) {
      // Abort the transaction on error
      if (session) {
        await session.abortTransaction();
      }
      logger.error('Error in migration process:', error);
      throw error;
    }
  } catch (error) {
    logger.error('Error migrating document lockers:', error);
    throw error;
  } finally {
    if (session) {
      await session.endSession();
    }
    await closeConnection();
  }
}

const getDocumentLockers = async (session: any): Promise<IDocumentLocker[]> => {
  try {
    // Parse CSV file with row numbers
    const csvPath = path.resolve(process.cwd(), 'data', 'document-locker.csv');
    const documentLockerDataWithRows = await parseCSVWithRowNumbers<ICsvDocumentLocker>(csvPath, CsvToObjectKeyMapDocumentLocker);

    if (!documentLockerDataWithRows || documentLockerDataWithRows.length === 0) {
      logger.info('No document locker data found in CSV file');
      return [];
    }

    logger.info(`Found ${documentLockerDataWithRows.length} document lockers in CSV file`);

    // Validate data and collect errors
    const validationErrors: string[] = [];
    const validDocumentLockers: Array<{ data: ICsvDocumentLocker, rowNumber: number }> = [];

    for (const { data: documentLocker, rowNumber } of documentLockerDataWithRows) {
      const errors = validateDocumentLockerData(documentLocker, rowNumber);
      if (errors.length > 0) {
        validationErrors.push(...errors);
      } else {
        validDocumentLockers.push({ data: documentLocker, rowNumber });
      }
    }

    if (validationErrors.length > 0) {
      logger.warn(`Found ${validationErrors.length} validation errors. Skipping invalid rows and continuing with valid data.`);
      logger.warn(`First 10 validation errors:\n${validationErrors.slice(0, 10).join('\n')}`);
    }

    if (validDocumentLockers.length === 0) {
      logger.error('No valid document locker records found after validation');
      return [];
    }

    logger.info(`Processing ${validDocumentLockers.length} valid document locker records out of ${documentLockerDataWithRows.length} total records`);

    // Get all unique user IDs from CSV
    const userIds = [...new Set(validDocumentLockers.map(item => item.data.id).filter(id => id))];

    // Fetch users from database
    const users = await User.find({
      organizationId: new Types.ObjectId(global.config.organizationId),
      id: { $in: userIds },
      role: global.roleMap.get(ENUM_ROLE_TYPE.USER)
    }).session(session).exec();

    // Create user ID to user document mapping
    const userIdMap = new Map<string, any>();
    users.forEach(user => {
      if (user.id) {
        userIdMap.set(user.id.toString(), user);
      }
    });

    // Get user ObjectIds for client lookup
    const userObjectIds = users.map(user => user._id);

    // Fetch clients to get facility information
    const clients = await Client.find({
      organizationId: new Types.ObjectId(global.config.organizationId),
      userId: { $in: userObjectIds }
    }).session(session).exec();

    // Create user ObjectId to client mapping
    const userToClientMap = new Map<string, any>();
    clients.forEach(client => {
      userToClientMap.set(client.userId.toString(), client);
    });

    // Process document lockers
    const documentLockers: IDocumentLocker[] = [];
    const skippedEntries: string[] = [];

    for (const { data: csvDocumentLocker, rowNumber } of validDocumentLockers) {
      const user = userIdMap.get(csvDocumentLocker.id);

      if (!user) {
        skippedEntries.push(`Row ${rowNumber}: User with id ${csvDocumentLocker.id} not found`);
        continue;
      }

      const client = userToClientMap.get(user._id.toString());

      if (!client) {
        skippedEntries.push(`Row ${rowNumber}: Client record for user ${csvDocumentLocker.id} not found`);
        continue;
      }

      // Generate document name
      const documentName = generateDocumentName(
        csvDocumentLocker.path,
        user._id.toString(),
        csvDocumentLocker.first_name,
        csvDocumentLocker.last_name,
        csvDocumentLocker.middle_name
      );

      const documentLocker = new DocumentLocker({
        userId: user._id,
        facilityId: client.facilityId,
        organizationId: global.config.organizationId,
        fileUrl: csvDocumentLocker.path,
        documentName: documentName,
        uploadedBy: global.config.organizationId,
      });

      documentLockers.push(documentLocker);
    }

    if (skippedEntries.length > 0) {
      logger.warn(`Skipped ${skippedEntries.length} entries:\n${skippedEntries.join('\n')}`);
    }

    if (documentLockers.length === 0) {
      logger.warn('No valid document lockers to save after processing');
      return [];
    }

    // Check for duplicate document lockers
    const existingDocumentLockers = await DocumentLocker.find({
      organizationId: new Types.ObjectId(global.config.organizationId),
      fileUrl: { $in: documentLockers.map(dl => dl.fileUrl) }
    }).session(session).exec();

    if (existingDocumentLockers.length > 0) {
      const duplicateUrls = existingDocumentLockers.map(dl => dl.fileUrl);
      logger.error(`Duplicate document lockers found: ${duplicateUrls.join(', ')}`);
      throw new Error(`Found ${existingDocumentLockers.length} duplicate document lockers`);
    }

    // Save all document lockers
    const savedDocumentLockers = await DocumentLocker.insertMany(documentLockers, { session });
    logger.info(`Successfully created ${savedDocumentLockers.length} document lockers`);

    return savedDocumentLockers;
  } catch (error) {
    logger.error('Error processing document lockers:', error);
    throw error;
  }
};
