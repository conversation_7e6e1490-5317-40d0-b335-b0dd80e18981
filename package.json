{"name": "hop-migration-scripts", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "index": "tsc && node dist/index.js", "build": "tsc", "migrate": "tsc && node dist/index.js", "migrate:test": "tsc && node dist/index.js test", "migrate:all": "tsc && node dist/index.js migrate-all", "migrate:user": "tsc && node dist/index.js user", "migrate:facility": "tsc && node dist/index.js facility", "migrate:service": "tsc && node dist/index.js service", "migrate:pricing": "tsc && node dist/index.js pricing", "migrate:invoice": "tsc && node dist/index.js invoice"}, "keywords": [], "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.888.0", "@aws-sdk/lib-storage": "^3.888.0", "@aws-sdk/s3-request-presigner": "^3.888.0", "@types/bcrypt": "^5.0.2", "@types/node": "^22.13.10", "@types/validator": "^13.15.3", "bcrypt": "^5.1.1", "chalk": "^5.4.1", "commander": "^13.1.0", "csv-parse": "^5.6.0", "dotenv": "^16.4.7", "inquirer": "^12.6.0", "log4js": "^6.9.1", "mime-types": "^3.0.1", "moment": "^2.30.1", "mongoose": "^8.12.1", "number-to-words": "^1.2.4", "ts-node": "^10.9.2", "typescript": "^5.8.2", "uuid4": "^2.0.3", "validator": "^13.15.15"}, "devDependencies": {"@types/uuid4": "^2.0.3"}}